1. doc
https://brainindustries.atlassian.net/wiki/spaces/SP/pages/**********/Industrial+API+Tutorial
https://nexus3.shirtplatform.com/repository/maven-site/webservices/apidocs/index.html

2. dashboard:
- dev
Shirtplatform editor URL:
https://pilot.shirtplatform.com/editor

htaccess:
username: senprints
password: S3nPr1ntz@SP2021

Login to your account:
username: SenPrints
password: v2aY36AV1

Here is an URL to the Production Tool:
https://pilot.shirtplatform.com/production-tool/login.xhtml

username: SenPrints_PT
password: 7tUXdbPy

- production
Shirtplatform editor URL:
https://editor.shirtplatform.com/editor/

htaccess:
username: senprintslive
password: p4dm-q0394t8f3=t9if-309tfu8n-q3094tfun-309tun

Login to your account:
username: Senprints
password: 9hsBpDhNSp

URL to the Production Tool:
https://api.shirtplatform.com/production-tool/login.xhtml

username: Senprints_PT
password: JtgeYD82Sp

3. webhook:
https://nexus3.shirtplatform.com/repository/maven-site/webservices/apidocs/index.html#quickstart-webhooks
https://nexus3.shirtplatform.com/repository/maven-site/webservices/apidocs/index.html#resource_Webhook
header: basic auth username:password (get from config/supplier.php)
body:
{
  "webhook" : {
    "address" : {url},
    "topic" : {topic}, //orders/updated, orders/fulfilled, orders/acknowledged, orders/failed
    "secret" : {token},
    "mediaType" : "JSON"
  }
}
response:
{
    "webhook": {
        "id": 19,
        "address": "https://long.ap.ngrok.io/public/test-webhook",
        "topic": {topic},
        "mediaType": "JSON"
    }
}
save id to config to test
https://pilot.shirtplatform.com/webservices/rest/accounts/{accountId}/shops/{shopId}/webhooks/{webhookId}/testOrderEvent/{orderId}
ex: https://pilot.shirtplatform.com/webservices/rest/accounts/197/shops/1051/webhooks/19/testOrderEvent/3274820

request:
- header:x-shirtplatform-hmac-sha256