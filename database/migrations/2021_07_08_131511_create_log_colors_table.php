<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLogColorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('log_colors', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('supplier_color_id')->nullable();
            $table->foreignId('supplier_id')
                ->constrained('user')
                ->onDelete('cascade');
            $table->string('hex_code')->nullable();
            $table->string('link')->nullable();
            $table->unique(['name', 'supplier_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
