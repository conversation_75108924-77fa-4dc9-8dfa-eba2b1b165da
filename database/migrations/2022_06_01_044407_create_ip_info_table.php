<?php

use App\Enums\CheckScamStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIpInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ip_info', function (Blueprint $table) {
            $table->ipAddress('ip_address')->primary();
            $table->string('location')->nullable()->index();
            $table->enum('status', CheckScamStatusEnum::getValues())->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
