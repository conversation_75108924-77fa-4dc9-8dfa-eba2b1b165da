<?php

namespace Database\Seeders;

use App\Models\SystemConfig;
use Illuminate\Database\Seeder;

class TwilioConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        SystemConfig::insert([
            [
                'key' => 'twilio_account_sid',
                'value' => config('senprints.twilio.account_sid'),
            ],
            [
                'key' => 'twilio_auth_token',
                'value' => config('senprints.twilio.auth_token'),
            ],
            [
                'key' => 'twilio_from_number',
                'value' => config('senprints.twilio.from_number'),
            ],
        ]);
    }
}
