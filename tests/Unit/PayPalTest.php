<?php

namespace Tests\Unit;

use App\Services\PayPal;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class PayPalTest extends TestCase
{
    protected function getPayPalClient(): PayPal
    {
        return new PayPal(['clientId' => 'test', 'secret' => 'test', 'testMode' => true]);
    }

    public function testGetAccessTokenSuccessfully(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response(['access_token' => 'test_token']),
        ]);

        $paypal = $this->getPayPalClient();
        $this->assertEquals('test_token', $paypal->getAccessToken());
    }

    public function testGetAccessTokenFailure(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response([], 400),
        ]);

        $this->expectException(\RuntimeException::class);

        $paypal = $this->getPayPalClient();
        $paypal->getAccessToken();
    }

    public function testCreateOrderSuccessfully(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response(['access_token' => 'test_token'], 200),
            'api-m.sandbox.paypal.com/v2/checkout/orders' => Http::response(['links' => [['rel' => 'payer-action', 'href' => 'test_url']]], 200),
        ]);

        $paypal = $this->getPayPalClient();
        $redirectUrl = $paypal->createOrder([], 'return_url', 'cancel_url', 'brand_name');
        $this->assertEquals('test_url', $redirectUrl);
    }

    public function testCreateOrderFailure(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response(['access_token' => 'test_token'], 200),
            'api-m.sandbox.paypal.com/v2/checkout/orders' => Http::response([], 400),
        ]);

        $this->expectException(\RuntimeException::class);

        $paypal = $this->getPayPalClient();
        $paypal->createOrder([], 'return_url', 'cancel_url', 'brand_name');
    }

    public function testCapturePaymentSuccessfully(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response(['access_token' => 'test_token'], 200),
            'api-m.sandbox.paypal.com/v2/checkout/orders/*' => Http::response(['status' => 'COMPLETED'], 200),
        ]);

        $res = $this->getPayPalClient()->capturePayment('test_order_id');
        $this->assertEquals('COMPLETED', $res['status']);
    }

    public function testCapturePaymentFailure(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v2/checkout/orders/*' => Http::response([], 400),
        ]);

        $this->expectException(\RuntimeException::class);

        $paypal = $this->getPayPalClient();
        $paypal->capturePayment('test_order_id');
    }

    public function testVerifyPurchaseSuccessfully(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response(['access_token' => 'test_token'], 200),
            'api-m.sandbox.paypal.com/v2/checkout/orders/*' => Http::response(['intent' => 'CAPTURE', 'status' => 'COMPLETED'], 200),
        ]);

        $paypal = $this->getPayPalClient();
        $this->assertTrue($paypal->verifyPurchase('test_order_id'));
    }

    public function testVerifyPurchaseFailure(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response(['access_token' => 'test_token'], 200),
            'api-m.sandbox.paypal.com/v2/checkout/orders/*' => Http::response(['intent' => 'CAPTURE', 'status' => 'PENDING'], 200),
        ]);

        $paypal = $this->getPayPalClient();
        $this->assertFalse($paypal->verifyPurchase('test_order_id'));
    }

    public function testRefundSuccessfully(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response(['access_token' => 'test_token'], 200),
            'api-m.sandbox.paypal.com/v2/payments/captures/*' => Http::response(['status' => 'COMPLETED'], 200),
        ]);

        $paypal = $this->getPayPalClient();
        $this->assertTrue($paypal->refund('test_capture_id', '10.00', 'USD', 'test_invoice_id', 'test_note', []));
    }

    public function testRefundFailure(): void
    {
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response(['access_token' => 'test_token'], 200),
            'api-m.sandbox.paypal.com/v2/payments/captures/*' => Http::response([], 400),
        ]);

        $this->expectException(\RuntimeException::class);

        $paypal = $this->getPayPalClient();
        $paypal->refund('test_capture_id', '10.00', 'USD', 'test_invoice_id', 'test_note', []);
    }
}
