<?php

namespace Modules\EmailMarketingKlaviyo\Jobs\Demo;

use App\Enums\OrderPaymentStatus;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\Order;
use App\Events\OrderPaymentCompleted;
use Modules\OrderService\Models\RegionOrders;
use Modules\Marketing\Supports\Helper;
use TheIconic\NameParser\Parser;
use App\Models\Store;
use Illuminate\Support\Str;
use App\Enums\OrderTypeEnum;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Services\StoreService;
use App\Enums\OrderStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSubscribeProfilesJob;
use Illuminate\Support\Carbon;

class KlaviyoDemoAddedToCartJob implements ShouldQueue
{
    use InteractsWithQueue;
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?User $seller;
    public $storeId;
    public ?Store $store;

    public function __construct($storeId, $sellerId = null)
    {
        $this->onQueue(KlaviyoQueueName::TrackEvents);
        if ($storeId) {
            $this->storeId = $storeId;
            $this->store = Store::whereId($storeId)->first();
        }
        else {
            $this->seller = User::query()->find($sellerId);
        }
    }

    public function handle(): void
    {
        if (empty($this->store) && empty($this->seller)) {
            return;
        }
        $privateKey = $this->store?->klaviyo_private_key ?? $this->seller?->klaviyo_private_key;;
        // $publicKey = $this->store->klaviyo_public_key ?? null;
        if (empty($privateKey)) {
            $this->release();
            return;
        }
        $baseUrl = env('APP_URL', 'https://senprints.com');
        $domain = parse_url($baseUrl, PHP_URL_HOST);
        $storeName = env('APP_NAME', 'SenPrints');
        $properties = [
            'AddedItemProductName' => $storeName . ' T-Shirt',
            'AddedItemProductID' => '12345',
            'AddedItemCategories' => ['T-Shirt'],
            'AddedItemURL' => $baseUrl . '/t-shirt',
            'AddedItemPrice' => 100,
            'AddedItemQuantity' => 1,
            'ImageURL' => 'https://' . $domain . '/t-shirt.jpg',
            'CheckoutURL' => $baseUrl . '/cart',
            '$value' => 100,
            'Items' => [
                [
                    'ProductID' => '12345',
                    'ProductName' => $storeName . ' T-Shirt',
                    'Quantity' => 1,
                    'ItemPrice' => 100,
                    'RowTotal' => 100,
                    'ProductURL' => $baseUrl . '/t-shirt',
                    'ImageURL' => 'https://' . $domain . '/t-shirt.jpg',
                    'ProductCategories' => ['T-Shirt'],
                ]
            ]
        ];
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => $properties,
                    "time" => Carbon::now()->toIso8601ZuluString(),
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Added to Cart"
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => 'noreply@' . $domain,
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $storeName,
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($privateKey, $payload));
    }

    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoDemoAddedToCartJob', [
            'storeId' => $this?->storeId,
            'sellerId' => $this->seller?->id,
        ]);
    }
}
