<?php

namespace Modules\EmailMarketingKlaviyo\Services;

use App\Models\Campaign;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\Store;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Modules\EmailMarketingKlaviyo\Data\OrderItemsData;
use Modules\EmailMarketingKlaviyo\Data\ProfileData;
use Modules\OrderService\Models\RegionOrders;
use TheIconic\NameParser\Parser;

class StoreService
{
    protected int $storeId;

    public function __construct()
    {
        //
    }

    /**
     * Set seller id
     *
     * @param int $storeId
     * @return self
     */
    public function setStoreId(int $storeId)
    {
        $this->storeId = $storeId;
        return $this;
    }

    /**
     * Get store information
     *
     * @return null|Store
     */
    public function getStore()
    {
        $store = Store::whereId($this->storeId)->first();
        if (!$store) {
            return null;
        }
        return $store;
    }

    /**
     * Get customers
     *
     * @param int $page
     * @param int $limit
     * @return Collection|null
     * @throws InvalidArgumentException
     */
    public function getCustomers($page = 1, $limit = 1000)
    {
        if (!$this->storeId) {
            throw new InvalidArgumentException("Store ID is required");
        }
        $offset = ($page - 1) * $limit;
        $subQuery = DB::table("seller_customer")
            ->select("customer_id")
            ->where("store_id", $this->storeId)
            ->limit($limit)
            ->offset($offset);
        $users = DB::table("customer")
            ->select("id", "name", "email", "phone")
            ->joinSub($subQuery, "subscribers", function ($join) {
                $join->on("customer.id", "=", "subscribers.customer_id");
            })
            ->get();

        if ($users->isEmpty()) {
            return Collection::empty();
        }
        $store = $this->getStore();
        if (!$store) {
            throw new InvalidArgumentException("Store not found: {$this->storeId}");
        }
        return $users->filter(function ($profile) {
            $isValidEmail = filter_var($profile->email, FILTER_VALIDATE_EMAIL);
            if (!$isValidEmail) {
                return false;
            }
            return $profile->name !== null && $profile->email !== null;
        })->map(function ($user) use ($store) {
            [$firstName, $lastName] = ProfileData::parseName($user->name);
            if (!$user->email) {
                throw new InvalidArgumentException("Email not found: {$user->email}");
            }
            return ProfileData::from([
                "email" => $user->email,
                "first_name" => $firstName,
                "last_name" => $lastName,
                "phone_number" => $user->phone,
                "properties" => [
                    "store_domain" => $store->domain,
                    "store_name" => $store->name,
                ],
            ]);
        });
    }

    /**
     * Get items from order
     * @param Order|RegionOrders $order
     * @return ?OrderItemsData
     */
    public static function getOrderItems(Order|RegionOrders $order): ?OrderItemsData
    {
        $orderProducts = OrderProduct::whereOrderId($order->id)
            ->with('product')
            ->get();
        if ($orderProducts->count() == 0) {
            graylogInfo("Order has no products", [
                "order_id" => $order->id,
                "category" => "email-marketing-klaviyo",
            ]);
            return null;
        }
        $itemNames = [];
        $categories = [];
        $storeBaseUrl = "https://" . $order->store_domain;
        $orderedItems = $orderProducts->map(function (OrderProduct $orderProduct) use (&$categories, &$itemNames, $storeBaseUrl, $order) {
            $seller = $orderProduct->seller;
            $product = Product::whereId($orderProduct->product_id)
                ->onSellerConnection($seller)
                ->with('category_names')
                ->first();
            if (empty($product)) {
                graylogInfo("Product not found", [
                    "order_id" => $order->id,
                    "product_id" => $orderProduct->product_id,
                    "category" => "email-marketing-klaviyo",
                ]);
                return null;
            }
            $campaign = Campaign::whereId($orderProduct->campaign_id)
                ->onSellerConnection($seller)
                ->first();
            if (empty($campaign)) {
                graylogInfo("Campaign not found", [
                    "order_id" => $order->id,
                    "campaign_id" => $orderProduct->campaign_id,
                    "category" => "email-marketing-klaviyo",
                ]);
                return null;
            }
            $categories = array_merge($categories, [$product->name]);
            $itemName = $campaign->name . " - " . $product->name;
            $itemNames = array_merge($itemNames, [$itemName]);
            $productUrl = $storeBaseUrl . $orderProduct->product_url;
            $variants = $orderProduct->options ?? "{}";
            $variantName = collect(json_decode($variants, true))
                ->map(function ($value, $key) {
                    return ucfirst($key) . ": " . ucfirst($value);
                })
                ->implode(", ");
            return (object) [
                "ProductID" => $orderProduct->product_id,
                "SKU" => $product->sku ?? "",
                "ProductName" => $itemName,
                "Quantity" => $orderProduct->quantity,
                "ItemPrice" => $orderProduct->price,
                "RowTotal" => $orderProduct->price * $orderProduct->quantity,
                "ProductURL" => $productUrl,
                "ImageURL" => imgUrl($orderProduct->thumb_url, 'thumb'),
                "Categories" => [$product->name],
                "Brand" => "",
                "VariantName" => $variantName,
            ];
        });
        $categories = array_unique($categories);
        return new OrderItemsData(
            item_names: $itemNames,
            categories: $categories,
            items: $orderedItems->toArray()
        );
    }

    /**
     * Get shipping address
     * @param Order|RegionOrders $order
     * @return object
     */
    public static function getShippingAddress(Order|RegionOrders $order)
    {
        if (empty($order->customer_name)) {
            return (object) [];
        }
        $nameParser = new Parser();
        $parsedName = $nameParser->parse($order->customer_name ?? "");
        return (object) [
            "FirstName" => $parsedName->getFirstname(),
            "LastName" => $parsedName->getLastname(),
            "Address1" => $order->address,
            "City" => $order->city,
            "Region" => $order->state ?? $order->region,
            "Country" => $order->country,
            "CountryCode" => $order->country,
            "Zip" => $order->postcode,
            "Phone" => $order->customer_phone,
        ];
    }

    /**
     * Get billing address
     *
     * @param Order|RegionOrders $order
     * @return object
     */
    public static function getBillingAddress(Order|RegionOrders $order)
    {
        $order->load(['customer_address']);
        $nameParser = new Parser();
        $customer = $order->customer_address;
        if (empty($customer) || empty($customer->name)) {
            return (object) [];
        }
        $parsedName = $nameParser->parse($customer->name);
        return (object) [
            "FirstName" => $parsedName->getFirstname(),
            "LastName" => $parsedName->getLastname(),
            "Address1" => $customer->address ?? "",
            "City" => $customer->city ?? "",
            "RegionCode" => $customer->state ?? "",
            "CountryCode" => $customer->country ?? "",
            "Zip" => $customer->postcode ?? "",
            "Phone" => $customer->phone ?? "",
        ];
    }

    /**
     * Get currency
     * @param Order|RegionOrders $order
     * @return string
     */
    public static function getCurrency(Order|RegionOrders $order): string
    {
        return empty($order->currency_code) ? "USD" : $order->currency_code;
    }
}
