<?php
namespace Modules\ShopifyApp\Providers;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Config;
use Modules\ShopifyApp\Http\Middleware\EnsureShopifySession;
use Modules\ShopifyApp\Http\Middleware\IframeProtection;
use Modules\ShopifyApp\Supports\Handlers\AppUninstalled;
use Modules\ShopifyApp\Supports\SessionStorage;
use Shopify\Context;
use Shopify\Webhooks\Registry;
use Shopify\Webhooks\Topics;

class ShopifyAppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap ShopifyApp service provider service.
     *
     * @return void
     */
    public function boot()
    {
        if(!defined('SHOPIFY_APP_MODULE_PATH')) {
            define('SHOPIFY_APP_MODULE_PATH', dirname(__DIR__, 2));
        }
        $this->autoload(SHOPIFY_APP_MODULE_PATH . '/helpers');
        $this->loadMigrationsFrom(SHOPIFY_APP_MODULE_PATH . '/database/migrations');
        $this->registerRoutes();
        $this->registerViews();
        $this->registerConfigs(['general']);
        $this->registerMiddlewares();
        $this->app->booted(function () {
            $this->app->register(CommandServiceProvider::class);
            $this->app->register(EventServiceProvider::class);
            if(!empty(Config::get('shopify.app.config.general.api_key')) &&
                !empty(Config::get('shopify.app.config.general.api_secret')) &&
                !empty(Config::get('shopify.app.config.general.host')) &&
                !empty(Config::get('shopify.app.config.general.scopes'))) {
                Context::initialize(
                    Config::get('shopify.app.config.general.api_key'),
                    Config::get('shopify.app.config.general.api_secret'),
                    Config::get('shopify.app.config.general.scopes'),
                    str_replace('https://', '', Config::get('shopify.app.config.general.host')),
                    new SessionStorage(),
                );
                Registry::addHandler(Topics::APP_UNINSTALLED, new AppUninstalled());
            }
            if (config('senprints.schedule_enabled')) {
                $schedule = $this->app->make(Schedule::class);
                $schedule->command('shopify:sync-orders')->everyFiveMinutes()->withoutOverlapping(10)->runInBackground()->logAfter();
                $schedule->command('shopify:sync-tracking-code')->everyFiveMinutes()->withoutOverlapping(10)->runInBackground()->logAfter();
            }
        });
    }

    /**
     * Register the shopify_app's configs.
     *
     * @return void
     */
    protected function registerConfigs($fileNames)
    {
        if (!is_array($fileNames)) {
            $fileNames = [$fileNames];
        }
        $config_path = SHOPIFY_APP_MODULE_PATH . '/config';
        foreach ($fileNames as $fileName) {
            $full_path = $config_path . '/' . $fileName . '.php';
            $this->mergeConfigFrom($full_path, 'shopify.app.config.' . $fileName);
        }
    }

    /**
     * Boot the middlewares for the package.
     *
     * @return void
     */
    protected function registerMiddlewares()
    {
        $this->app['router']->aliasMiddleware('shopify.auth', EnsureShopifySession::class);
        $this->app->booted(function () {
            $this->app['router']->pushMiddlewareToGroup('web', IframeProtection::class);
        });
    }

    /**
     * Boot the views for the package.
     *
     * @return void
     */
    private function registerViews()
    {
        $viewResourcesPath = SHOPIFY_APP_MODULE_PATH . '/resources/views';
        $this->loadViewsFrom($viewResourcesPath, 'shopify-app');
        $this->publishes([
            $viewResourcesPath => resource_path('views/vendor/shopify-app'),
        ], 'shopify-views');
    }


    /**
     * Register the shopify_app's routes.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }
        $route_path = SHOPIFY_APP_MODULE_PATH . '/routes';
        $routes = $this->scanFolder($route_path);
        foreach ($routes as $route) {
            $this->loadRoutesFrom($route_path . '/' . $route);
        }
    }
}
