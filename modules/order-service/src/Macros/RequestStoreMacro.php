<?php
namespace Modules\OrderService\Macros;

use App\Services\StoreService;
use Closure;

class RequestStoreMacro
{
    /**
     * @return Closure
     */
    public function currentStoreInfo(): Closure
    {
        return function () {
            return StoreService::getCurrentStoreInfo();
        };
    }

    /**
     * @return Closure
     */
    public function storeDomain(): Closure
    {
        return function () {
            return StoreService::currentDomain();
        };
    }

    /**
     * @return Closure
     */
    public function cfIpCountry(): Closure
    {
        return function () {
            return $this->header('cf-ipcountry');
        };
    }

    /**
     * @return Closure
     */
    public function clientFingerprint(): Closure
    {
        return function () {
            $fingerprint = $this->input('visit_info.device_id');

            if (!$fingerprint) {
                $fingerprint = $this->header('x-client-fingerprint');
            }
            return $fingerprint ?? null;
        };
    }

    /**
     * @return Closure
     */
    public function currentVersion(): Closure
    {
        return function () {
            return trim($this->header('X-VERSION') ?? '3');
        };
    }

    /**
     * @return Closure
     */
    public function isForceRegionCheckout(): Closure
    {
        return function () {
            return (int)trim($this->header('FORCE-DISTRIBUTED-CHECKOUT') ?? '0');
        };
    }
}
