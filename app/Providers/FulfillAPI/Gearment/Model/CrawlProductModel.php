<?php

namespace App\Providers\FulfillAPI\Gearment\Model;

use App\Enums\ProductOptionEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Arr;

class CrawlProductModel extends AbstractModel
{
    // key: supplier's variant  => value: senprint's variant
    private const COLOR_VALUE_MAPPING = [
        'Irish' => 'irish green',
        'Cardinal' => 'cardinal red',
    ];

    public function mapping(array $product): array
    {
        $arr['product'] = [
            'name'         => $product['product_name'],
            'sku'          => $product['product_id'],
            'thumb_url'    => $product['product_img'],
            'print_areas'  => $this->getPrintAreas($product),
            'product_type' => $this->product_type,
            'supplier_id'  => $this->supplier_id,
        ];
        $this->setAttributes($arr, $product);
        return $arr;
    }

    /**
     * @throws \Throwable
     */
    private function getPrintAreas($product): ?string
    {
        if (empty($product['print_areas'])) {
            return null;
        }

        $arr = [];
        foreach ($product['print_areas'] as $printArea) {
            $arr[] = $this->setAndGetOption(
                new CrawlProductVariantModel(),
                ProductOptionEnum::PRINT,
                $printArea,
                $product,
            );
        }

        return json_encode($arr, JSON_THROW_ON_ERROR);
    }

    private function setAttributes(&$arr, $product): void
    {
        $optionsProduct = [];
        $model = new CrawlProductVariantModel();
        foreach ($product['variants'] as $variant) {
            $options = [];
            $this->variantValueMapping($variant['color']);
            $this->reformatOptionForSpecificProduct($product['product_name'], $variant['color']);
            if (!empty($variant['color'])) {
                $this->setAndGetOption(
                    $model,
                    ProductOptionEnum::COLOR,
                    $variant['color'],
                    $product,
                    $options,
                    $optionsProduct,
                );
            }

            $this->variantValueMapping($variant['size'], ProductOptionEnum::SIZE);

            if (!empty($variant['size'])) {
                $this->setAndGetOption(
                    $model,
                    ProductOptionEnum::SIZE,
                    $variant['size'],
                    $product,
                    $options,
                    $optionsProduct,
                );
            }
            $arr['variants'][] = $model::mapping($variant, $options);
        }

        $arr['product']['options'] = !empty($optionsProduct) ? json_encode($optionsProduct) : null;
    }

    /**
     * @param $variant
     * @param string $type
     * @return void
     * */
    private function variantValueMapping (&$variant, string $type = ProductOptionEnum::COLOR): void {
        if ($type === ProductOptionEnum::COLOR) {
            $newVariant = Arr::get(self::COLOR_VALUE_MAPPING, $variant);
            if (isset($newVariant)) {
                $variant = $newVariant;
            }
        } else if (($type === ProductOptionEnum::SIZE) && preg_match('/^\d+x\d+$/', $variant)) {
            $newVariant = $variant . 'in';
            $variant = $newVariant;
        }

    }

    /**
     * @param $productName
     * @param $option
     * @param $type
     * @return void
     */
    private function reformatOptionForSpecificProduct($productName, &$option, $type = ProductOptionEnum::COLOR): void
    {
        $availableProduct = [
            'Accent Mug 110 Accent Mug 11oz'
        ];

        if (in_array($productName, $availableProduct)) {
            switch ($productName) {
                case ('Accent Mug 110 Accent Mug 11oz'):
                    if ($type == ProductOptionEnum::COLOR) {
                        $option = $option . ' accent';
                    }
                    break;
                default:
                    break;
            }
        }

    }
}
