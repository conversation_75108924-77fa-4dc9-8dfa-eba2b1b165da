<?php

namespace App\Providers\FulfillAPI\Onos\Model;

use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    public string $customer_note;
    public string $identifier = 'API';
    public array $items = [];
    public string $note = '';
    public string $order_id;
    public string $order_name = '';
    public string $reference_id;
    public array $shipping_info;
    public array $tracking;

    public function setOrder($order): void
    {
        $this->customer_note    = $order->customer_note ?? '';
        $this->fulfill_order_id = $this->order_id = $this->reference_id = $this->getReferenceId($order);
        $this->setShippingInfo($order);
        $this->shipping_method = 'ONOSEXPRESS';
        $isLabelOrder = $order->shipping_label;
        if (!empty($isLabelOrder)) {
            $this->shipping_method = 'SBTT';
            $this->setTracking($order);
        }
    }

    /**
     * @param $order
     * @return void
     */
    private function setTracking ($order): void {
        $label =  $order->shipping_label ? s3Url($order->shipping_label) : null;
        $firstProduct = $order->products->first();
        if (!empty($firstProduct)) {
            $this->tracking = [
                'tracking_number' => $firstProduct->tracking_code,
                'carrier' => $firstProduct->shipping_carrier,
                'link_print' => $label
            ];
        }

    }

    private function setShippingInfo($order): void
    {
        $this->shipping_info = [
            'full_name' => $order->customer_name,
            'address_1' => $this->customer_address['primary'],
            'address_2' => $this->customer_address['addition'],
            'city'      => $order->city,
            'country'   => $order->country,
            'email'     => $order->customer_email ?? '',
            'phone'     => $order->customer_phone ?? '',
            'postcode'  => $order->postcode ?? '',
            'state'     => $order->state ?? '',
        ];
    }

    public function setItems($product): void
    {
        [$designBack, $designFront, $designHood, $image] = $this->getLinkImages($product);
        $fulfillProduct = $this->getProductFulfill($product);
        $attributes     = $this->getAttributes($fulfillProduct->name);

        $this->items[] = [
            'attributes'   => $attributes,
            'currency'     => 'USD',
            'design_back'  => $designBack,
            'design_front' => $designFront,
            'design_hood'  => $designHood,
            'image'        => $image,
            'name'         => $this->getReferenceId($product),
            'price'        => 0,
            'product_id'   => $fulfillProduct->sku,
            'quantity'     => $product->quantity,
            'sku'          => $product->fulfill_sku,
        ];
    }

    private function getLinkImages($product): array
    {
        $designBack = $designFront = $designHood = $image = '';

        foreach ($product->files as $file) {
            $printSpace = $this->getByMapping(
                $file->print_space,
                FulfillMappingEnum::PRINT_SPACES
            );
            if (empty($image)) {
                $image = $file->mockup_url;
            }

            if ($printSpace === 'back') {
                $designBack = $file->design_url;
            } elseif ($printSpace === 'front') {
                $designFront = $file->design_url;
            } elseif ($printSpace === 'hood') {
                $designHood = $file->design_url;
            }
        }

        return [$designBack, $designFront, $designHood, $image];
    }

    private function getAttributes($fulfillProductName): array
    {
        $arr   = [];
        $arr[] = [
            'name'   => 'product',
            'option' => $fulfillProductName,
        ];

        return $arr;
    }
}
