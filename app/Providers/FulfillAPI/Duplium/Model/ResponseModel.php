<?php

namespace App\Providers\FulfillAPI\Duplium\Model;

use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\DupliumWebhookRepository;
use Illuminate\Support\Arr;

class ResponseModel extends AbstractResponseModel
{
    protected function setErrors($arr): void
    {
        if ($arr['response_status_code'] === 200) {
            return;
        }
        $this->errors[] = Arr::get($arr, 'message');
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['reference_id']);
    }

    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $fulfillOrderId,
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        if ($arr['response_status_code'] === 200) {
            return (new DupliumWebhookRepository($arr, $this->supplier_id))
                ->withCrawlMode()
                ->get();
        }

        return [];
    }
}
