<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Models\Order;
use App\Providers\FulfillAPI\PrintArrow\Model\ResponseModel;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class PrintArrowWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    // statues must lower key
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING => [
            ResponseModel::WAITING_FOR_APPROVE,
        ],
        FulfillmentStatusEnum::PROCESSING => [
            ResponseModel::IN_PRODUCTION,
        ],
        FulfillmentStatusEnum::FULFILLED => [
            ResponseModel::DELIVERED,
            ResponseModel::IN_TRANSIT,
            ResponseModel::SHIPPED,
        ],
        FulfillmentStatusEnum::REJECTED => [
            ResponseModel::CANCELED,
        ],
    ];

    protected array $statusesCancelled = [
        ResponseModel::CANCELED,
    ];

    protected array $statusesOnHold = [
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response;
    }

    /**
     * @return $this
     */
    public function webhookHandle(): self
    {
        return $this;
    }


    /*
     *
     * {
            "items": [
                {
                    "custom_order_id": "121551",
                    "status": "In Production",
                    "discount_amount": 0,
                    "grand_total": 22.98,
                    "shipping_amount": 8.49,
                    "subtotal": 14.49,
                    "tax_amount": 0,
                    "created_at": "2025-03-17 03:51:33",
                    "updated_at": "2025-03-17 03:51:38",
                    "increment_id": "MKP000752818",
                    "total_item_count": 1
                }
            ],
            "search_criteria": {
                "filter_groups": [
                    {
                        "filters": [
                            {
                                "field": "custom_order_id",
                                "value": "121551",
                                "condition_type": "eq"
                            }
                        ]
                    }
                ]
            },
            "total_count": 1
        }
     * */

    /**
     * @return $this
     */
    public function crawlHandle(): self
    {
        $items = Arr::get($this->data, 'items');
        if (count($items) < 1) {
            $this->setFulfillmentStatus(
                FulfillmentStatusEnum::EXCEPTION
            );
            $this->setExceptionMessage(
                'Not found any items'
            );
            return $this;
        }
        $item = $items[0];
        $status = Arr::get($item, 'status');
        $customOrderId = Arr::get($item, 'custom_order_id');
        $supplierOrderId =  Arr::get($item, 'increment_id');
        $this->setSenPrintsOrderId($customOrderId);
        $this->setSupplierOrderId($supplierOrderId);

        $this->setFulfillmentStatus($this->fulfillStatusMapping($this->arrStatusMapping, $status));
        $data = [
            'tracking' => [
                'tracking_code' => Arr::get($item, 'track_number', ''),
                'shipping_carrier' => 'USPS',
            ]
        ];
        if (empty($items)) {
            $data['no_items'] = true;
        } else {
            $data['items'] = $items;
        }
        $this->setFulfillments([$data]);
        return $this;
    }
}
