<?php

namespace App\Models;

use App\Enums\OrderTypeEnum;
use App\Enums\ProductType;
use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\Company
 *
 * @property int $id
 * @property int $order_id
 * @property int $order_product_id
 * @property int $supplier_id
 * @property bool $cross_shipping
 * @property string|null $notified
 * @property int $created_by
 * @property int|null $fulfill_order_id
 * @property string|null $fulfill_status
 * @property int $fulfilled
 * @property \Illuminate\Support\Carbon|null $fulfilled_at
 * @property \Illuminate\Support\Carbon $created_at
 * @method static \Database\Factories\CompanyFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Company newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Company newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Company query()
 * @method static \Illuminate\Database\Eloquent\Builder|Company whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Company whereId($value)
 * @mixin \Illuminate\Database\Query\Builder
 * @mixin \Illuminate\Database\Eloquent\Builder
 */
class OrderAssignSupplierHistory extends Model
{
    use HasFactory, Filterable;

    protected $table = 'order_assign_supplier_history';

    protected $fillable = [
        'order_id',
        'order_product_id',
        'supplier_id',
        'notified',
        'cross_shipping',
        'fulfill_sku',
        'fulfill_product_id',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function order(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplier(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function creator(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Staff::class, 'created_by');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function orderProduct(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(OrderProduct::class, 'order_product_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function fulfillProduct(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Product::class, 'fulfill_product_id')
            ->where('product_type', ProductType::FULFILL_PRODUCT);
    }

    /**
     * Create a new record from OrderProduct.
     *
     * @param     \App\Models\OrderProduct     $op
     *
     * @return \App\Models\OrderAssignSupplierHistory
     */
    public static function createFromOrderProduct(OrderProduct $op): OrderAssignSupplierHistory
    {
        $model = new static([
            'order_id' => $op->order_id,
            'order_product_id' => $op->id,
            'supplier_id' => $op->supplier_id,
            'fulfill_sku' => $op->fulfill_sku,
            'fulfill_product_id' => $op->fulfill_product_id,
            'cross_shipping' => $op->isCrossShipping(),
            'created_by' => optional(currentUser())->getUserId(),
        ]);

        $model->save();

        return $model;
    }

    /**
     * @param     \App\Models\OrderProduct     $op
     *
     * @return \App\Models\OrderAssignSupplierHistory|null
     */
    public static function updateFulfillFromOrderProduct(OrderProduct $op): ?OrderAssignSupplierHistory
    {
        if ($row = self::latestOfOrderProduct($op)) {
            $row->fulfill_order_id = $op->fulfill_order_id;
            $row->fulfill_status = $op->fulfill_status;
            $row->fulfilled = true;
            $row->fulfilled_at = now();
            $row->save();
        }

        return $row;
    }

    /**
     * Tại một thời điểm, ví dụ như đẩy đơn sẽ chỉ có một bản ghi phù hợp với
     * thông tin của đơn - đó là bản ghi mới nhất
     *
     * @param     \App\Models\OrderProduct     $op
     *
     * @return \App\Models\OrderAssignSupplierHistory|null
     */
    public static function latestOfOrderProduct(OrderProduct $op): ?OrderAssignSupplierHistory
    {
        return self::query()
            ->where('order_product_id', $op->id)
            ->where('fulfill_product_id', $op->fulfill_product_id)
            ->where('fulfill_sku', $op->fulfill_sku)
            ->orderByDesc('created_at')
            ->first();
    }

    /**
     * Load records that have not been notified yet.
     *
     * @param     int     $take
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function loadHasNotBeenNotified(int $take = 100): \Illuminate\Database\Eloquent\Collection
    {
        return static::query()
            ->whereHas('order', function ($query) {
                $query->whereIn('type', [OrderTypeEnum::SERVICE, OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM]);
            })
            ->whereNull('notified')
            ->take($take)
            ->get();
    }

    /**
     * @param     string     $kind
     *
     * @return boolean
     */
    public function updateNotified(string $kind): bool
    {
        return $this->update(['notified' => $kind]);
    }

    /**
     * @param     string     $kind
     *
     * @return boolean
     */
    public function updateCanceled(string $kind): bool
    {
        return $this->update(['notified' => 'cancel_' . $kind]);
    }
}
