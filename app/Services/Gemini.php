<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class Gemini
{
    private const ENDPOINT = 'https://generativelanguage.googleapis.com';
    private $apiKey;
    private $fileUri = null;

    public function __construct(string $apiKey = '')
    {
        if (empty($apiKey)) {
            $this->apiKey = config('services.google_gemini.api_key');

            if (empty($this->apiKey)) {
                throw new \RuntimeException('API key is required');
            }
        } else {
            $this->apiKey = $apiKey;
        }
    }

    /**
     * This method may be unnecessary, but kept for reference.
     *
     * @param string $file
     * @param string $mimeType
     * @return void
     */
    public function uploadFile(string $file, string $mimeType): void
    {
        $numBytes = filesize($file);
        $response = Http::withHeaders([
            'X-Goog-Upload-Command' => 'start, upload, finalize',
            'X-Goog-Upload-Header-Content-Length' => $numBytes,
            'X-Goog-Upload-Header-Content-Type' => $mimeType,
            'Content-Type' => 'application/json',
            'x-goog-api-key' => $this->apiKey
        ])->post(self::ENDPOINT . '/upload/v1beta/files', [
            'file' => ['display_name' => basename($file)]
        ]);

        if ($response->successful()) {
            $this->fileUri = $response->json('file.uri');
        } else {
            throw new \RuntimeException('File upload failed: ' . $response->body());
        }
    }

    public function scanImage(string $imageUrl)
    {
        $image = file_get_contents($imageUrl);

        if ($image === false) {
            return null;
        }

        $base64image = base64_encode($image);

        $contents = [
            [
                'role' => 'user',
                'parts' => [
                    [
                        'inline_data' => [
                            'mime_type' => 'image/jpeg',
                            'data' => $base64image
                        ]
                    ]
                ]
            ]
        ];

        $systemInstruction = [
            'role' => 'user',
            'parts' => [
                [
                    'text' => 'You are an AI system designed to analyze images and identify elements that may be related to copyright. Your task is to detect and flag any components within the image that could potentially infringe on copyright, including but not limited to:
                    1. Text: Brand names, product names, company names, or any other text that could be an indication of intellectual property rights.
                    2. Logos: Symbols, brand identifiers, or any graphic elements that may be associated with a trademark or copyright.
                    3. Design patterns: Distinctive design styles or graphic elements protected by intellectual property rights.
                    4. Famous personalities: Images, likenesses, or representations of well-known public figures, which may be protected under publicity rights or trademark laws.
                    5. Artworks and Illustrations: Artistic works, drawings, or any other illustrative content that may be protected by copyright.
                    6. Photographs: Photos or any other imagery, especially if they belong to a specific photographer or organization.
                    7. Videos and Frames: Video clips or frames extracted from videos that may be related to copyright, especially if cited without permission.
                    8. Audio-Visual Content: Audio, music, or other audiovisual elements that may appear within the image, if any.
                    9. Product Designs: Product designs, including packaging, advertising materials, or specific products that may be protected by copyright or patent.
                    10. Architectural Works: Architectural designs or structures that may be related to copyright, especially when they are clearly depicted in the image.

                    ### Requirements:
                    - Thorough analysis: Inspect the entire image to identify and flag potential copyright-related elements.
                    - High accuracy: Identify elements clearly related to copyright while being cautious to avoid false positives.
                    - Confidence scoring: Assign a confidence score to each detected element, indicating the likelihood that the element is related to copyright. This score should range from 0 to 1, where 1 indicates the highest confidence.
                    - Detailed reporting: Provide a detailed description of any identified elements, including their position in the image, the confidence score, and the reason for flagging.
                    - Response in JSON format: Ensure that the response is structured in a valid JSON string format.

                    ### Objective:
                    Your objective is to assist users in identifying and addressing copyright-related issues in images. You need to ensure that users are clearly informed about any copyright elements present in the image, including potential issues related to public figures, and can make appropriate decisions based on the provided information.

                    ### Response Structure (JSON)
                    {
                      "report": {
                        "type": "array",
                        "items": {
                          "type": "object",
                          "properties": {
                            "element_type": {
                              "type": "string",
                              "enum": [
                                "Text",
                                "Logo",
                                "Design pattern",
                                "Famous personality",
                                "Artwork",
                                "Photograph",
                                "Video frame",
                                "Audio-Visual content",
                                "Product design",
                                "Architectural work"
                              ]
                            },
                            "element_content": {
                              "type": "string",
                              "description": "If the element is logo, don\'t include \'logo\' in the content. E.g.: \'Apple\' instead of \'Apple logo\'."
                            },
                            "position": {
                              "type": "string",
                                "description": "The position of the element in the image, e.g., top-left corner, center, etc."
                            },
                            "confidence_score": {
                              "type": "number",
                              "minimum": 0,
                              "maximum": 1,
                              "description": "A value between 0 and 1, where 1 indicates the highest confidence. E.g.: 0.8 means 80% confidence."
                            }
                          },
                          "required": [
                            "element_type",
                            "element_content",
                            "position",
                            "confidence_score"
                          ],
                          "additionalProperties": false
                        }
                      }
                    }'
                ]
            ]
        ];

        $generationConfig = [
            'temperature' => 1,
            'topK' => 64,
            'topP' => 0.95,
            'maxOutputTokens' => 8192,
            'responseMimeType' => 'application/json'
        ];

        $response = Http::asJson()
            ->withHeader('x-goog-api-key', $this->apiKey)
            ->post(self::ENDPOINT . '/v1beta/models/gemini-1.5-flash:generateContent', [
                'contents' => $contents,
                'systemInstruction' => $systemInstruction,
                'generationConfig' => $generationConfig
            ]);

        if ($response->successful()) {
            return $response->json('candidates.0.content.parts.0.text');
        }

        return null;
    }
}
