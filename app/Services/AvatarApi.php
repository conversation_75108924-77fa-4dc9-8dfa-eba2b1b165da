<?php

namespace App\Services;

use App\Enums\CacheKeys;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class AvatarApi
{
    protected $apiEndpoint;
    protected $username;
    protected $password;

    public function __construct()
    {
        $this->apiEndpoint = config('avatar-api.api_endpoint');
        $this->username = config('avatar-api.username');
        $this->password = config('avatar-api.password');
    }

    public static function getProfile(string $email)
    {
        return (new self)->sendRequest('GetProfile', $email);
    }

    protected function handle(string $url, string $target): array
    {
        if (substr($url, 0, 1) !== '/') {
            $url = '/' . $url;
        }

        $parameters = [
            'username' => $this->username,
            'password' => $this->password
        ];

        switch ($url) {
            case '/GetProfile':
            case '/VerifyEmail':
                $parameters['email'] = $target;
                break;
            case '/GetProfileByPhone':
                $parameters['phone'] = $target;
                break;
        }

        return [$url, $parameters];
    }

    protected function sendRequest(string $url, string $target)
    {
        [$url, $data] = $this->handle($url, $target);

        try {
            $response = Http::withoutVerifying()
                ->timeout(22)
                ->get($this->apiEndpoint . $url, $data);
        } catch (\Throwable $e) {
            // ignore timeout exception
            if ($e->getCode() !== 28 || !Str::contains($e->getMessage(), 'Operation timed out')) {
                if (Cache::get('avatar_api_error')) {
                    return false;
                }

                Cache::add('avatar_api_error', $e->getMessage(), CacheKeys::CACHE_1H);
                logToDiscord('[Avatar-Api] GET request failed to ' . $url . ' - ' . $e->getMessage(), 'avatar-api-log');
            }

            return false;
        }

        if ($response->failed()) {
            if (Cache::get('avatar_api_error')) {
                return false;
            }

            // System.Exception: Out of credit, please top up via paypal
            if (($response->status() === 500) && Str::contains($response->body(), 'Out of credit')) {
                Cache::add('avatar_api_error', '[Avatar-Api] Out of credit, please top up via paypal', CacheKeys::CACHE_1H);
                logToDiscord('[Avatar-Api] Out of credit, please top up via paypal', 'avatar-api-log');
            } else {
                Cache::add('avatar_api_error', '[Avatar-Api] GET request failed to ' . $url . ' - Status: ' . $response->status(), CacheKeys::CACHE_1H);
                logToDiscord('[Avatar-Api] GET request failed to ' . $url . ' - Status: ' . $response->status(), 'avatar-api-log');
            }

            return false;
        }

        $response = simplexml_load_string($response->body());

        return json_decode(json_encode($response), true);
    }
}
