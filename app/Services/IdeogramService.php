<?php

namespace App\Services;

use App\Enums\UserInfoKeyEnum;
use App\Jobs\MigrateIdeogramImagesToLocal;
use App\Models\AiImageGeneration;
use App\Models\UserInfo;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use InvalidArgumentException;

class IdeogramService
{
    protected ?string $apiKey;
    protected string $baseUrl = 'https://api.ideogram.ai';

    // Valid aspect ratios
    public const ASPECT_RATIO_1_1 = 'ASPECT_1_1';
    public const ASPECT_RATIO_16_9 = 'ASPECT_16_9';
    public const ASPECT_RATIO_9_16 = 'ASPECT_9_16';
    public const ASPECT_RATIO_4_3 = 'ASPECT_4_3';
    public const ASPECT_RATIO_3_4 = 'ASPECT_3_4';
    public const ASPECT_RATIO_10_16 = 'ASPECT_10_16';
    public const ASPECT_RATIO_16_10 = 'ASPECT_16_10';
    public const ASPECT_RATIO_3_2 = 'ASPECT_3_2';
    public const ASPECT_RATIO_2_3 = 'ASPECT_2_3';
    public const ASPECT_RATIO_1_3 = 'ASPECT_1_3';
    public const ASPECT_RATIO_3_1 = 'ASPECT_3_1';

    // Valid models
    public const MODEL_V1 = 'V_1';
    public const MODEL_V1_TURBO = 'V_1_TURBO';
    public const MODEL_V2 = 'V_2';
    public const MODEL_V2_TURBO = 'V_2_TURBO';
    public const MODEL_V2A = 'V_2A';
    public const MODEL_V2A_TURBO = 'V_2A_TURBO';

    // Valid magic prompt options
    public const MAGIC_PROMPT_AUTO = 'AUTO';
    public const MAGIC_PROMPT_NONE = 'NONE';

    // Valid style types
    public const STYLE_TYPE_AUTO = 'AUTO';
    public const STYLE_TYPE_GENERAL = 'GENERAL';
    public const STYLE_TYPE_REALISTIC = 'REALISTIC';
    public const STYLE_TYPE_DESIGN = 'DESIGN';
    public const STYLE_TYPE_RENDER_3D = 'RENDER_3D';
    public const STYLE_TYPE_ANIME = 'ANIME';

    // Valid image types for upscale
    private const ALLOWED_IMAGE_TYPES = [
        'image/jpeg',
        'image/png',
        'image/webp'
    ];

    /**
     * @throws \Exception
     */
    public function __construct()
    {
        $this->apiKey = null;
        $isLocalDev = app()->isLocal();

        // In local/dev environment, try to use API key from config
        if ($isLocalDev) {
            $this->apiKey = config('services.ideogram.api_key');
            if (!empty($this->apiKey)) {
                return;
            }
        }

        // For production or if config API key is not set, try to get user's API key
        try {
            // Try to get the user's API key if they are logged in
            $userId = currentUser()->getUserId();

            if (!$userId) {
                // In local/dev, don't throw exception if no user is logged in
                if ($isLocalDev) {
                    return;
                }
                throw new \RuntimeException('User ID not found. Please login to use this feature.');
            }

            $userApiKey = UserInfo::where('user_id', $userId)
                ->where('key', UserInfoKeyEnum::IDEOGRAM_API_KEY)
                ->value('value');

            if (empty($userApiKey)) {
                // In local/dev, don't throw exception if user has no API key
                if ($isLocalDev) {
                    return;
                }
                throw new \RuntimeException('User API key not found. Please add your API key in settings.');
            }

            $this->apiKey = $userApiKey;
        } catch (\Exception $e) {
            // In local/dev, silently fail if any error occurs during user API key retrieval
            if (!$isLocalDev) {
                throw $e;
            }
        }
    }

    /**
     * Validate image file type
     *
     * @param string $mimeType
     * @return bool
     */
    private function isValidImageType(string $mimeType): bool
    {
        return in_array($mimeType, self::ALLOWED_IMAGE_TYPES);
    }

    /**
     * Check if API key is available
     *
     * @return bool
     */
    public function hasApiKey(): bool
    {
        return !empty($this->apiKey);
    }

    /**
     * Throw exception if API key is not available
     *
     * @throws \Exception
     */
    private function validateApiKey(): void
    {
        if (!$this->hasApiKey()) {
            if (app()->isLocal()) {
                throw new \RuntimeException(
                    'Ideogram API key is not available. ' .
                    'Please add IDEOGRAM_API_KEY to your .env file or add your API key in user settings.'
                );
            }

            throw new \RuntimeException('Ideogram API key is not available. Please add your API key in settings.');
        }
    }

    /**
     * Generate images from text prompt
     *
     * @param string $prompt
     * @param int $numImages
     * @param string $aspectRatio
     * @param string $model
     * @param string $magicPromptOption
     * @param string|null $negativePrompt
     * @param string|null $styleType
     * @param int|null $seed
     * @param bool $saveHistory
     * @return array|null
     * @throws \Exception
     */
    public function generateImage(
        string  $prompt,
        int     $numImages = 1,
        string  $aspectRatio = self::ASPECT_RATIO_16_9,
        string  $model = self::MODEL_V2A,
        string  $magicPromptOption = self::MAGIC_PROMPT_AUTO,
        ?string $negativePrompt = null,
        ?string $styleType = null,
        ?int    $seed = null,
        bool    $saveHistory = true
    ): ?array
    {
        try {
            $this->validateApiKey();

            // Validate numImages is within allowed range
            $numImages = max(1, min(8, $numImages));

            // Prepare request data
            $imageRequest = [
                'prompt' => $prompt,
                'num_images' => $numImages,
                'aspect_ratio' => $aspectRatio,
                'model' => $model,
                'magic_prompt_option' => $magicPromptOption
            ];

            // Add optional parameters if provided
            if ($negativePrompt !== null) {
                $imageRequest['negative_prompt'] = $negativePrompt;
            }

            if ($styleType !== null) {
                $imageRequest['style_type'] = $styleType;
            }

            // Add seed only if it's within the valid range (0-2147483647)
            if ($seed !== null && $seed >= 0 && $seed <= 2147483647) {
                $imageRequest['seed'] = $seed;
            }

            $response = Http::withHeaders([
                'Api-Key' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/generate', [
                'image_request' => $imageRequest
            ]);

            $responseData = $response->json();
            $isSuccessful = $response->successful() && isset($responseData['data']);

            // Save history
            $userId = currentUser()->getUserId();

            if ($saveHistory && $userId && $isSuccessful) {
                // Extract image URLs from response
                $resultImages = array_map(function ($image) {
                    return $image['url'] ?? null;
                }, $responseData['data']);

                // Create history record
                $aiImage = AiImageGeneration::create([
                    'user_id' => $userId,
                    'prompt' => $prompt,
                    'negative_prompt' => $negativePrompt,
                    'aspect_ratio' => $aspectRatio,
                    'model' => $model,
                    'magic_prompt_option' => $magicPromptOption,
                    'style_type' => $styleType,
                    'seed' => $seed,
                    'num_images' => $numImages,
                    'result_images' => $resultImages,
                ]);

                // migrate images from Ideogram to our server
                if ($aiImage && !empty($resultImages)) {
                    MigrateIdeogramImagesToLocal::dispatch($aiImage->id);
                }
            }

            if ($isSuccessful) {
                return $responseData;
            }

            graylogError('Ideogram API Error', [
                'category' => 'ideogram_api',
                'status' => $response->status(),
                'response' => $responseData,
                'prompt' => $prompt,
                'aspect_ratio' => $aspectRatio,
                'num_images' => $numImages,
                'negative_prompt' => $negativePrompt,
                'style_type' => $styleType,
                'seed' => $seed
            ]);

            return null;
        } catch (\Exception $e) {
            graylogError('Ideogram Service Error', [
                'category' => 'ideogram_api',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'prompt' => $prompt,
                'aspect_ratio' => $aspectRatio,
                'num_images' => $numImages,
                'negative_prompt' => $negativePrompt,
                'style_type' => $styleType,
                'seed' => $seed
            ]);

            return null;
        }
    }

    /**
     * Upscale an image using Ideogram API
     *
     * @param UploadedFile|string $image The image file to upscale (can be UploadedFile or path to file)
     * @param string|null $prompt Optional prompt to guide the upscaling process
     * @param string $model The model version to use (use class constants MODEL_*)
     * @return array|null The response from Ideogram API
     * @throws InvalidArgumentException If the image type is not supported
     */
    public function upscaleImage(
        UploadedFile|string $image,
        ?string             $prompt = null,
        string              $model = self::MODEL_V2A
    ): ?array
    {
        try {
            $this->validateApiKey();

            $imageFile = $image instanceof UploadedFile ? $image->path() : $image;

            // Get mime type
            $mimeType = $image instanceof UploadedFile
                ? $image->getMimeType()
                : mime_content_type($imageFile);

            // Validate image type
            if (!$this->isValidImageType($mimeType)) {
                throw new InvalidArgumentException(
                    'Invalid image type: ' . $mimeType . '. Supported types are: ' . implode(', ', self::ALLOWED_IMAGE_TYPES)
                );
            }

            $imageRequest = [
                'model' => $model
            ];

            if ($prompt !== null) {
                $imageRequest['prompt'] = $prompt;
            }

            // Check if file exists and is readable
            if (!is_file($imageFile)) {
                throw new \RuntimeException('Image file does not exist: ' . $imageFile);
            }

            $fileContent = @file_get_contents($imageFile);
            if ($fileContent === false) {
                throw new \RuntimeException('Failed to read image file: ' . $imageFile);
            }

            // Use cURL directly to create a proper multipart request
            $ch = curl_init($this->baseUrl . '/upscale');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);

            // Prepare multipart data with clear boundary
            $boundary = uniqid();
            $delimiter = '-------------' . $boundary;

            $postData = '';

            // Add image_request part
            $postData .= "--" . $delimiter . "\r\n";
            $postData .= 'Content-Disposition: form-data; name="image_request"' . "\r\n\r\n";
            $postData .= json_encode($imageRequest) . "\r\n";

            // Add image_file part
            $postData .= "--" . $delimiter . "\r\n";
            $postData .= 'Content-Disposition: form-data; name="image_file"; filename="' . basename($imageFile) . '"' . "\r\n";
            $postData .= 'Content-Type: ' . $mimeType . "\r\n\r\n";
            $postData .= $fileContent . "\r\n";

            // End multipart data
            $postData .= "--" . $delimiter . "--\r\n";

            // Set headers
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Api-Key: ' . $this->apiKey,
                'Content-Type: multipart/form-data; boundary=' . $delimiter,
                'Content-Length: ' . strlen($postData)
            ]);

            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

            // Execute request
            $responseBody = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                graylogError('Ideogram cURL Error', [
                    'category' => 'ideogram_api',
                    'error' => $error,
                    'image' => basename($imageFile),
                    'prompt' => $prompt
                ]);

                return [
                    'error' => true,
                    'message' => 'cURL Error: ' . $error
                ];
            }

            if ($httpCode >= 200 && $httpCode < 300) {
                // Parse JSON result from responseBody
                if (empty($responseBody)) {
                    return [
                        'error' => true,
                        'message' => 'API returned empty result'
                    ];
                }

                $responseData = json_decode($responseBody, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    graylogError('Failed to parse JSON response', [
                        'category' => 'ideogram_api',
                        'error' => json_last_error_msg(),
                        'response_body' => substr($responseBody, 0, 1000)
                    ]);

                    return [
                        'error' => true,
                        'message' => 'API returned invalid data: ' . json_last_error_msg()
                    ];
                }

                // Check response structure
                if (!is_array($responseData)) {
                    graylogError('Ideogram API returned non-array response', [
                        'category' => 'ideogram_api',
                        'response_type' => gettype($responseData),
                        'response_body' => substr($responseBody, 0, 1000)
                    ]);

                    return [
                        'error' => true,
                        'message' => 'API returned non-array data'
                    ];
                }

                // Check if response contains necessary data
                if (!isset($responseData['data'])) {
                    graylogError('Ideogram API response missing data field', [
                        'category' => 'ideogram_api',
                        'response_keys' => array_keys($responseData),
                        'response_body' => substr($responseBody, 0, 1000)
                    ]);

                    return [
                        'error' => true,
                        'message' => 'Data field not found in API response',
                        'response' => $responseData
                    ];
                }

                // Check data is an array and has at least 1 element
                if (!is_array($responseData['data']) || empty($responseData['data'])) {
                    graylogError('Ideogram API data field empty or not array', [
                        'category' => 'ideogram_api',
                        'data_type' => gettype($responseData['data']),
                        'is_empty' => empty($responseData['data']),
                        'response_body' => substr($responseBody, 0, 1000)
                    ]);

                    return [
                        'error' => true,
                        'message' => 'Data field in API response is empty or not an array',
                        'response' => $responseData
                    ];
                }

                // Check if first element contains URL
                if (!isset($responseData['data'][0]['url'])) {
                    graylogError('Ideogram API response missing URL in data', [
                        'category' => 'ideogram_api',
                        'data_first_keys' => isset($responseData['data'][0]) ? array_keys($responseData['data'][0]) : [],
                        'response_body' => substr($responseBody, 0, 1000)
                    ]);

                    return [
                        'error' => true,
                        'message' => 'Image URL not found in API response',
                        'response' => $responseData
                    ];
                }

                return $responseData;
            }

            // Handle error when API fails
            $jsonResponse = null;

            if (!empty($responseBody)) {
                $jsonResponse = json_decode($responseBody, true);
            }

            graylogError('Ideogram API Error', [
                'category' => 'ideogram_api',
                'status' => $httpCode,
                'response' => $jsonResponse,
                'image' => basename($imageFile),
                'prompt' => $prompt,
                'raw_response' => substr($responseBody, 0, 1000) // Truncate to avoid log overflow
            ]);

            return [
                'error' => true,
                'message' => 'API returned HTTP error: ' . $httpCode,
                'status_code' => $httpCode,
                'response' => $jsonResponse,
                'raw_response' => substr($responseBody, 0, 500)
            ];
        } catch (\Exception $e) {
            graylogError('Ideogram Service Error', [
                'category' => 'ideogram_api',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'image' => $image instanceof UploadedFile ? $image->getClientOriginalName() : basename($image),
                'prompt' => $prompt,
                'error_code' => $e->getCode()
            ]);

            return [
                'error' => true,
                'message' => 'Image processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get description for an image using Ideogram API
     *
     * @param UploadedFile|string $image The image file to describe (can be UploadedFile or path to file)
     * @return array|null The response from Ideogram API containing descriptions
     * @throws InvalidArgumentException If the image type is not supported
     */
    public function describeImage(UploadedFile|string $image): ?array
    {
        try {
            $this->validateApiKey();

            $imageFile = $image instanceof UploadedFile ? $image->path() : $image;

            // Get mime type
            $mimeType = $image instanceof UploadedFile
                ? $image->getMimeType()
                : mime_content_type($imageFile);

            // Validate image type
            if (!$this->isValidImageType($mimeType)) {
                throw new InvalidArgumentException(
                    'Invalid image type. Supported types are: ' . implode(', ', self::ALLOWED_IMAGE_TYPES)
                );
            }

            $response = Http::withHeaders([
                'Api-Key' => $this->apiKey,
            ])->attach(
                'image_file',
                file_get_contents($imageFile),
                basename($imageFile)
            )->post($this->baseUrl . '/describe');

            if ($response->successful()) {
                return $response->json();
            }

            graylogError('Ideogram API Error', [
                'category' => 'ideogram_api',
                'status' => $response->status(),
                'response' => $response->json(),
                'image' => basename($imageFile)
            ]);

            return null;
        } catch (\Exception $e) {
            graylogError('Ideogram Service Error', [
                'category' => 'ideogram_api',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'image' => $image instanceof UploadedFile ? $image->getClientOriginalName() : basename($image)
            ]);

            return null;
        }
    }

    /**
     * Upscale an image from URL using Ideogram API
     *
     * @param string $imageUrl The URL of the image to upscale
     * @param string|null $prompt Optional prompt to guide the upscaling process
     * @param string $model The model version to use (use class constants MODEL_*)
     * @return array|null The response from Ideogram API
     */
    public function upscaleImageFromUrl(
        string  $imageUrl,
        ?string $prompt = null,
        string  $model = self::MODEL_V2
    ): ?array
    {
        try {
            $this->validateApiKey();

            // Prepare request data
            $tempFile = tempnam(sys_get_temp_dir(), 'ideogram_');

            // Download file from URL with clear error handling
            $imageContent = @file_get_contents($imageUrl);
            if ($imageContent === false) {
                $error = error_get_last();
                $errorMessage = $error ? ($error['message'] ?? 'Unknown error') : 'Unknown error';
                throw new \RuntimeException('Failed to download image from URL: ' . $imageUrl . '. Error: ' . $errorMessage);
            }

            if (empty($imageContent)) {
                throw new \RuntimeException('Downloaded image content is empty from URL: ' . $imageUrl);
            }

            file_put_contents($tempFile, $imageContent);

            // Get mime type
            $mimeType = mime_content_type($tempFile);

            // Validate image type
            if (!$this->isValidImageType($mimeType)) {
                unlink($tempFile); // Clean up temp file
                throw new InvalidArgumentException(
                    'Invalid image type: ' . $mimeType . '. Supported types are: ' . implode(', ', self::ALLOWED_IMAGE_TYPES)
                );
            }

            // Call upscaleImage with temp file
            $result = $this->upscaleImage($tempFile, $prompt, $model);

            // Clean up temp file
            if (is_file($tempFile)) {
                unlink($tempFile);
            }

            return $result;
        } catch (\Exception $e) {
            // Make sure to delete temporary file if it still exists
            if (isset($tempFile) && is_file($tempFile)) {
                unlink($tempFile);
            }

            graylogError('Ideogram Service Error', [
                'category' => 'ideogram_api',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'image_url' => $imageUrl,
                'prompt' => $prompt,
                'error_code' => $e->getCode()
            ]);

            return [
                'error' => true,
                'message' => 'Image processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Remix an image with a new prompt
     *
     * @param UploadedFile|string $image The image file to remix (can be UploadedFile or path to file)
     * @param string $prompt The text prompt for image generation
     * @param string $aspectRatio The aspect ratio of the output image (use class constants ASPECT_RATIO_*)
     * @param int $imageWeight Weight of the original image (1-100)
     * @param string $magicPromptOption Magic prompt option (use class constants MAGIC_PROMPT_*)
     * @param string $model The model version to use (use class constants MODEL_*)
     * @param int $numImages Number of images to generate (1-8)
     * @param string|null $negativePrompt Things to exclude from the generated image
     * @param string|null $styleType The style of the generated image (use class constants STYLE_TYPE_*)
     * @param int|null $seed Seed for reproducible generation (0-2147483647)
     * @param bool $saveHistory Whether to save generation history (default: true)
     * @return array|null The response from Ideogram API
     */
    public function remixImage(
        UploadedFile|string $image,
        string              $prompt,
        string              $aspectRatio = self::ASPECT_RATIO_16_9,
        int                 $imageWeight = 50,
        string              $magicPromptOption = self::MAGIC_PROMPT_AUTO,
        string              $model = self::MODEL_V2A,
        int                 $numImages = 1,
        ?string             $negativePrompt = null,
        ?string             $styleType = null,
        ?int                $seed = null,
        bool                $saveHistory = true
    ): ?array
    {
        try {
            $this->validateApiKey();

            $imageFile = $image instanceof UploadedFile ? $image->path() : $image;

            // Get mime type
            $mimeType = $image instanceof UploadedFile
                ? $image->getMimeType()
                : mime_content_type($imageFile);

            // Validate image type
            if (!$this->isValidImageType($mimeType)) {
                throw new InvalidArgumentException(
                    'Invalid image type. Supported types are: ' . implode(', ', self::ALLOWED_IMAGE_TYPES)
                );
            }

            // Validate numImages is within allowed range
            $numImages = max(1, min(8, $numImages));

            // Validate imageWeight is within allowed range (1-100)
            $imageWeight = max(1, min(100, $imageWeight));

            // Prepare request data
            $imageRequest = [
                'prompt' => $prompt,
                'aspect_ratio' => $aspectRatio,
                'image_weight' => $imageWeight,
                'magic_prompt_option' => $magicPromptOption,
                'model' => $model,
                'num_images' => $numImages
            ];

            // Add optional parameters if provided
            if ($negativePrompt !== null) {
                $imageRequest['negative_prompt'] = $negativePrompt;
            }

            if ($styleType !== null) {
                $imageRequest['style_type'] = $styleType;
            }

            // Add seed only if it's within the valid range (0-2147483647)
            if ($seed !== null && $seed >= 0 && $seed <= 2147483647) {
                $imageRequest['seed'] = $seed;
            }

            // Use cURL directly to create a proper multipart request
            // Don't use Http::attach() as it doesn't support custom headers
            // Note: This is a workaround for the lack of support in Http client
            $ch = curl_init($this->baseUrl . '/remix');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);

            // Prepare multipart data
            $boundary = uniqid();
            $delimiter = '-------------' . $boundary;

            $postData = '';

            // Add image_request
            $postData .= "--" . $delimiter . "\r\n";
            $postData .= 'Content-Disposition: form-data; name="image_request"' . "\r\n\r\n";
            $postData .= json_encode($imageRequest) . "\r\n";

            // Add image_file
            $postData .= "--" . $delimiter . "\r\n";
            $postData .= 'Content-Disposition: form-data; name="image_file"; filename="' . basename($imageFile) . '"' . "\r\n";
            $postData .= 'Content-Type: ' . $mimeType . "\r\n\r\n";
            $postData .= file_get_contents($imageFile) . "\r\n";

            // End of multipart data
            $postData .= "--" . $delimiter . "--\r\n";

            // Set headers
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Api-Key: ' . $this->apiKey,
                'Content-Type: multipart/form-data; boundary=' . $delimiter,
                'Content-Length: ' . strlen($postData)
            ]);

            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

            // Execute request
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                graylogError('Ideogram API cURL Error', [
                    'category' => 'ideogram_api',
                    'error' => $error,
                    'prompt' => $prompt
                ]);
                return null;
            }

            $responseData = json_decode($response, true);
            $isSuccessful = $httpCode >= 200 && $httpCode < 300 && $responseData !== null;

            // Save history
            $userId = currentUser()->getUserId();

            if ($saveHistory && $userId && $isSuccessful) {
                $resultImages = null;

                // Extract image URLs from response
                if (isset($responseData['data']) && is_array($responseData['data'])) {
                    $resultImages = array_map(function ($image) {
                        return $image['url'] ?? null;
                    }, $responseData['data']);
                } elseif (isset($responseData['images'])) {
                    // Fallback for old API response format
                    $resultImages = array_map(function ($image) {
                        return $image['url'] ?? null;
                    }, $responseData['images']);
                }

                // Create history record
                $aiImage = AiImageGeneration::create([
                    'user_id' => $userId,
                    'prompt' => $prompt,
                    'negative_prompt' => $negativePrompt,
                    'aspect_ratio' => $aspectRatio,
                    'model' => $model,
                    'magic_prompt_option' => $magicPromptOption,
                    'style_type' => $styleType,
                    'seed' => $seed,
                    'num_images' => $numImages,
                    'result_images' => $resultImages,
                ]);

                // migrate images from Ideogram to our server
                if ($aiImage && !empty($resultImages)) {
                    MigrateIdeogramImagesToLocal::dispatch($aiImage->id);
                }
            }

            if ($isSuccessful) {
                return $responseData;
            }

            graylogError('Ideogram API Error', [
                'category' => 'ideogram_api',
                'status' => $httpCode,
                'response' => $responseData,
                'prompt' => $prompt,
                'aspect_ratio' => $aspectRatio,
                'num_images' => $numImages,
                'negative_prompt' => $negativePrompt,
                'style_type' => $styleType,
                'seed' => $seed,
                'image_weight' => $imageWeight
            ]);

            return null;
        } catch (\Exception $e) {
            graylogError('Ideogram Service Error', [
                'category' => 'ideogram_api',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'prompt' => $prompt,
                'aspect_ratio' => $aspectRatio,
                'num_images' => $numImages,
                'negative_prompt' => $negativePrompt,
                'style_type' => $styleType,
                'seed' => $seed,
                'image_weight' => $imageWeight
            ]);

            return null;
        }
    }
}
