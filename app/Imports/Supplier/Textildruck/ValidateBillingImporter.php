<?php

namespace App\Imports\Supplier\Textildruck;

use App\Enums\OrderProductFulfillStatus;
use App\Imports\Supplier\BaseValidateBillingImporter;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

/**
 * Class ValidateBillingImporter
 *
 * Raw data item sample:
 */
class ValidateBillingImporter  extends BaseValidateBillingImporter implements WithMultipleSheets
{
    public ?bool $isItemFile = null;

    /**
     * Vì mỗi row là tương ứng order product nên chúng ta không được collapse quantity
     *
     * @param     array     $row
     *
     * @return bool
     */
    public function shouldUnifyQuantity(array $row): bool
    {
        return false;
    }

    /**
     * Map fulfill order id
     *
     * @override BaseValidateBillingImporter::mapFulfillOrderId()
     *
     * @param     array     $row
     *
     * @return string|null
     */
    public function mapFulfillOrderId(array $row): ?string
    {
        return $row['order_no'];
    }

    /**
     * Đ<PERSON> phân biệt đang import file nào thì dùng cột sku
     * File excel shipping có không có sku
     * File excel item có sku
     *
     * @override BaseValidateBillingImporter::mapOrderId()
     *
     * @param     array     $row
     *
     * @return string|null
     */
    public function mapOrderId(array $row): ?string
    {
        return $row['order_no'];
    }

    /**
     * Get quantity
     *
     * @override BaseValidateBillingImporter::mapQuantity()
     *
     * @param     array     $row
     *
     * @return int|null
     */
    public function mapQuantity(array $row): ?int
    {
        return $row['qty'] ?? null;
    }

    /**
     * Get fulfill sku
     *
     * @override BaseValidateBillingImporter::mapFulfillSku()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapFulfillSku(array $row): ?string
    {
        return $row['raw_article_no'] ?? null;
    }

    /**
     * Chỉ có tracking code ở sheet shipping
     *
     * @override BaseValidateBillingImporter::mapTrackingCode()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapTrackingCode(array $row): ?string
    {
        return $row['tracking_no'] ?? '';
    }

    /**
     * Map fulfill base cost
     *
     * @override BaseValidateBillingImporter::mapFulfillBaseCost()
     *
     * @param     array     $row
     *
     * @return float
     */
    public function mapFulfillBaseCost(array $row): ?float
    {
        if ($this->isItemFile) {
            return (float) $row['raw_artile_costs'];
        }

        return null;
    }

    /**
     * Map fulfill shipping cost
     *
     * @override BaseValidateBillingImporter::mapFulfillShippingCost()
     *
     * @param     array     $row
     *
     * @return float
     */
    public function mapFulfillShippingCost(array $row): ?float
    {
        if ($this->isItemFile) {
            return null;
        }

        return $row['price'];
    }

    /**
     * @override BaseValidateBillingImporter::orderNotFound()
     *
     * @param $rows
     *
     * @return void
     */
    public function orderNotFound($rows): void
    {
        if (! $this->isItemFile) {
            $rows = $rows->all();
        }

        parent::orderNotFound($rows);
    }

    /**
     * @override BaseValidateBillingImporter::stopAtFirstError()
     *
     * @return bool
     */
    public function stopAtFirstError(): bool
    {
        return false;
    }

    /**
     * @param     \Illuminate\Support\Collection     $orderProducts
     * @param     \Illuminate\Support\Collection     $rows
     *
     * @return void
     */
    public function validateRows(Collection $orderProducts, Collection $rows): void
    {
        if ($this->isItemFile) {
            parent::validateRows($orderProducts, $rows);
        } else {
            $this->validateRowsFileShipping($orderProducts, $rows);
        }
    }

    /**
     * @param     \Illuminate\Support\Collection     $orderProducts
     * @param     \Illuminate\Support\Collection     $rows
     *
     * @return void
     */
    public function validateRowsFileShipping(Collection $orderProducts, Collection $rows): void
    {
        $order = $rows->first();
        $errors = [];

        // Cảnh báo nếu có 1 sản phẩm nào đó chưa được giao
        $statuses = [OrderProductFulfillStatus::FULFILLED, OrderProductFulfillStatus::ON_DELIVERY];
        $every = $orderProducts->every(static fn($op) => in_array($op->fulfill_status, $statuses, true));
        if (! $every) {
            $errors[] = static::ERR_UNFULFILLED;
        }

        // Cảnh báo nếu phí ship của supplier lớn hơn phí ship của sen charge seller
        $shippingCost = $orderProducts->sum('fulfill_shipping_cost');
        if ($shippingCost < $order['fulfill_shipping_cost']) {
            $errors[] = static::ERR_SHIPPING_COST;
        }

        // Cảnh báo nếu có 1 sản phẩm nào đó có tracking code khác với tracking code của order
        if (! $orderProducts->every(fn($op) => $op->tracking_code === $order['tracking_code'])) {
            $errors[] = static::ERR_TRACKING_CODE;
        }

        if ($errors) {
            $order['errors'] = implode('; ', $errors);
            $this->errors->push($order);
        }
    }

    /**
     * @return string[]
     */
    public function validators(): array
    {
        return [
            'billedValidator',
            'quantityValidator',
            'fulfillStatusValidator',
            'fulfillBaseCostValidator',
        ];
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        return [
            1 => tap(new static($this->supplierId()), static function($importer) {
                $importer->isItemFile = true;
            }),
            3 => tap(new static($this->supplierId()), function($importer) {
                $importer->isItemFile = false;
            }),
        ];
    }
}
