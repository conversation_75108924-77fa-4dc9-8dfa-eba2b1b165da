<?php

namespace App\Imports\Seller\SocialFeed;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ExcludeIdsImport implements ToArray, WithHeadingRow
{
    protected $seller_id;

    public function __construct($sellerId)
    {
        $this->seller_id = $sellerId;
    }

    public function array(array $arr): void
    {
        $keys = array_column($arr, 'item_id');
        if (empty($keys)) {
            throw new \RuntimeException('Empty column Item ID. Please check');
        }
        $ids = [];
        foreach ($keys as $key) {
            $explodes = explode('|', $key);
            $ids[] = $explodes[0];
        }

        foreach (array_chunk($ids, 1000) as $chunk) {
            Product::query()
                ->whereIn('id', $chunk)
                ->when($this->seller_id !== 1, function ($query) {
                    $query->where('seller_id', $this->seller_id);
                })
                ->update(
                    [
                        'google_category_id' => '-1', // ignore
                        'sync_status' => 0,
                    ]
                );
        }
    }
}
