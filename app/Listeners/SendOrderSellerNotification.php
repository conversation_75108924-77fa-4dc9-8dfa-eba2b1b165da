<?php

namespace App\Listeners;

use App\Enums\OrderTypeEnum;
use App\Enums\UserInfoKeyEnum;
use App\Events\AfterOrderPaymentCompleted;
use App\Http\Controllers\EmailController;
use App\Models\UserInfo;

class SendOrderSellerNotification
{
    /**
     * Handle the event.
     *
     * @param AfterOrderPaymentCompleted $event
     * @return void
     */
    public function handle(AfterOrderPaymentCompleted $event)
    {
        $order = $event->order;

        // check user settings
        $shouldSendEmail = UserInfo::query()
            ->where([
                'user_id' => $order->seller_id,
                'key' => UserInfoKeyEnum::EMAIL_NOTIFICATION_NEW_ORDER,
            ])
            ->value('value');

        if ($shouldSendEmail === null) {
            // default to true
            $shouldSendEmail = true;
        } else {
            $shouldSendEmail = (bool) $shouldSendEmail;
        }

        if ($order->type === OrderTypeEnum::SERVICE || $order->isCustomServiceOrder()) {
            $shouldSendEmail = false;
        }

        if (!$shouldSendEmail) {
            return;
        }

        try {
            /**
             * Send order seller notification email to seller
             */
            app(EmailController::class)->newOrderConfirmation($event->order);
        } catch (\Exception $ex) {
            logException($ex);
        }
    }
}
