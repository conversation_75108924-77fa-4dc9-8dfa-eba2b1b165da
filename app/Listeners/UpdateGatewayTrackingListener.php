<?php

namespace App\Listeners;

use App\Enums\PaymentMethodEnum;
use App\Events\OrderProductFulfilled;
use App\Http\Controllers\Storefront\PaypalController;
use App\Http\Controllers\Storefront\StripeController;
use App\Services\SeventeenTrack;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class UpdateGatewayTrackingListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(OrderProductFulfilled $event): void
    {
        $order = $event->order;
        if (!$order->transaction_id) {
            return;
        }
        $tracking = $event->tracking;
        if (empty($tracking['shipping_carrier']) && !empty($tracking['tracking_code'])) {
            $carrier = SeventeenTrack::detectCarrier($tracking['tracking_code'], null, null, $tracking['tracking_url']);
            if ($carrier) {
                $tracking['shipping_carrier'] = SeventeenTrack::carriers()->where('key', $carrier)->value('_name');
            }
        }
        if (PaymentMethodEnum::isStripe($order->payment_method)) {
            (new StripeController())->updateTracking($order, $tracking);
        } elseif ($order->payment_method === PaymentMethodEnum::PAYPAL) {
            (new PaypalController())->updateTracking($order, $tracking);
        }
    }
}
