<?php

namespace App\Traits;

use App\Library\Crisp\Crisp;
use App\Models\Order;

trait ContactForm
{
    /**
     * @throws \Exception
     */
    private static function sendToCrisp(array $data): array
    {
        $websiteId = config('crisp.website_id');
        $identifier = config('crisp.identifier');
        $key = config('crisp.key');

        try {
            $crisp = new Crisp($websiteId, $identifier, $key);
            $result = $crisp->sendContactForm($data);

            if (empty($result)) {
                throw new \RuntimeException('Send crisp fail');
            }
            if (!empty($result['sessionId'])) {
                $sessionId = $result['sessionId'];
                $toEmail = $crisp->getCC();
                if ($toEmail) {
                    dispatch(function () use ($toEmail, $sessionId) {
                        $websiteId = config('crisp.website_id');
                        $identifier = config('crisp.identifier');
                        $key = config('crisp.key');
                        $crisp = new Crisp($websiteId, $identifier, $key);
                        $crisp->sendTranscriptConversation($toEmail, $sessionId);
                    })->delay(now()->addMinute());
                }
            }
            return $result;
        } catch (\Exception $e) {
            graylogInfo("Send crisp fail: {$data['customer_email']}", [
                'category' => 'send_crisp',
                'email' => $data['customer_email'],
                'subject' => $data['subject'],
                'error' => $e,
                'data' => $data
            ]);

            throw $e;
        }
    }

    private static function getOrderStatusUrl($orderNumber, $customerEmail, $store = null): ?string
    {
        $order = !empty($orderNumber) ? Order::query()->select(['access_token', 'store_domain'])->firstWhere('order_number', $orderNumber) : null;
        if ($order === null) {
            $order = Order::query()
                ->select(['access_token', 'store_domain'])
                ->where('customer_email', $customerEmail)
                ->when(data_get($store, 'base_url'), function ($query) use ($store) {
                    return $query->where('store_domain', data_get($store, 'base_url'));
                })
                ->orderByDesc('paid_at')
                ->first();
        }
        return $order?->status_url;
    }
}
