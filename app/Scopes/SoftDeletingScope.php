<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class SoftDeletingScope extends Eloquent\SoftDeletingScope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->where($model->getQualifiedIsDeletedColumn(), 0);
    }

    protected function getIsDeletedColumn(Builder $builder)
    {
        if (count($builder->getQuery()->joins) > 0) {
            return $builder->getModel()->getQualifiedIsDeletedColumn();
        }

        return $builder->getModel()->getIsDeletedColumn();
    }

    public function extend(Eloquent\Builder $builder): void
    {
        foreach ($this->extensions as $extension) {
            $this->{"add{$extension}"}($builder);
        }

        $builder->onDelete(function (Eloquent\Builder $builder) {
            $deletedAtColumn = $this->getDeletedAtColumn($builder);
            $deletedHashColumn = $builder->getModel()->getIsDeletedColumn();

            return $builder->update([
                $deletedAtColumn => $builder->getModel()->freshTimestampString(),
                $deletedHashColumn => 1,
            ]);
        });
    }

    protected function addRestore(Eloquent\Builder $builder): void
    {
        $builder->macro('restore', function (Eloquent\Builder $builder) {
            $builder->withTrashed();

            return $builder->update([
                $builder->getModel()->getDeletedAtColumn() => null,
                $builder->getModel()->getIsDeletedColumn() => 0,
            ]);
        });
    }

    protected function addWithoutTrashed(Builder $builder)
    {
        $builder->macro('withoutTrashed', function (Builder $builder) {
            $model = $builder->getModel();

            $builder->withoutGlobalScope($this)
                ->where($model->getIsDeletedColumn(), 0);

            return $builder;
        });
    }

    protected function addOnlyTrashed(Builder $builder)
    {
        $builder->macro('onlyTrashed', function (Builder $builder) {
            $model = $builder->getModel();

            $builder->withoutGlobalScope($this)
                ->where($model->getIsDeletedColumn(), 1);

            return $builder;
        });
    }
}