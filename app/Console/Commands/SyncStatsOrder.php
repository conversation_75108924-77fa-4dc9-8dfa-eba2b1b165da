<?php

namespace App\Console\Commands;

use App\Enums\OrderPaymentStatus;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\BoughtTogetherLog;
use App\Models\Order;
use App\Models\StatsOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncStatsOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:stats-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Order Statistics (orders/items/sales/profit)';

    protected $orderIds = [];

    /**
     * Execute the console command.
     *
     * @param int $hour
     * @return int
     * @throws \Throwable
     */
    public function handle($hour = 24): int
    {
//        Log::info('===========Begin: Sync Stats Order===========');
        try {
//            Log::info('===========Begin: Get orders===========');
            $orders = Order::getOrdersByStatStatus()
                ->where('paid_at', '>', now()->subHours($hour))
                ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                ->where('total_seller_profit', '>', 0)
                ->limit(100)
                ->get();
//            Log::info('===========End: Get orders===========');
            if ($orders->count() > 0) {
//                Log::info('===========Begin: Get products by order===========');
                [$products, $ids, $orderWithProductIds, $productIds] = get_list_product_for_stats_order($orders);
//                Log::info('===========End: Get products by order===========');
                if (!empty($products)) {
                    try {
//                        Log::info('===========Begin: Insert data===========');
                        if (!empty($orderWithProductIds)) {
                            $boughtTogethers = [];
                            foreach ($orderWithProductIds as $orderId => $item) {
                                if (!isset($item['product_id'])) {
                                    continue;
                                }

                                $boughtTogethersProductIds = bought_together_product_ids($item['product_id']);
                                $boughtTogethersCampaignIds = bought_together_product_ids($item['campaign_id']);
                                $boughtTogethersTemplateIds = bought_together_product_ids($item['template_id']);
                                foreach ($boughtTogethersProductIds as $index => $boughtTogethersProductItem) {
                                    [$productId1, $productId2] = $boughtTogethersProductItem;
                                    [$campaignId1, $campaignId2] = $boughtTogethersCampaignIds[$index];
                                    [$templateId1, $templateId2] = $boughtTogethersTemplateIds[$index];
                                    $boughtTogethers[] = [
                                        'order_id' => $orderId,
                                        'product_id1' => $productId1,
                                        'product_id2' => $productId2,
                                        'campaign_id1' => $campaignId1,
                                        'campaign_id2' => $campaignId2,
                                        'template_id1' => $templateId1,
                                        'template_id2' => $templateId2,
                                        'customer_id' => $item['customer_id'],
                                        'seller_id' => $item['seller_id'],
                                        'store_id' => $item['store_id'],
                                        'timestamp' => $item['paid_at'],
                                    ];
                                }
                                usleep(800000);
                            }

                            // Array with chunk: fix 7 number of parameters must be between 0 and 65535
                            // Link: https://stackoverflow.com/questions/40361164/pdoexception-sqlstatehy000-general-error-7-number-of-parameters-must-be-bet
                            $boughtTogethersChunkArray = array_chunk($boughtTogethers, 200, true);
                            if (!empty($boughtTogethers) && !empty($boughtTogethersChunkArray)) {
                                foreach ($boughtTogethersChunkArray as $boughtTogethersChunk) {
                                    if (!empty($boughtTogethersChunk)) {
                                        BoughtTogetherLog::query()->insert($boughtTogethersChunk);
                                    }
                                }
                            }
                        }

                        if (StatsOrder::query()->insert($products)) {
                            Order::updateSyncStatusByIds($ids, Order::SYNC_DATA_STATS_DISABLED);
                        }

                        (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($productIds);
                    } catch (\Exception $e) {
                        Log::error("Message: {$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}");
                    }
                }
            }
        } catch (\Exception $e) {
            Log::info('===========Error Sync PostgreSQL===========');
            Log::error($e);
        }
//        Log::info('===========End: Sync Stats Order===========');
        return 0;
    }
}
