<?php

namespace App\Console\Commands;

use App\Enums\AccessExternalApiType;
use App\Enums\ApiLogTypeEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\SupplierEnum;
use App\Http\Controllers\Admin\FulfillController;
use App\Models\ApiLog;
use App\Repositories\PrintGeekWebhookRepository;
use App\Repositories\QTCOWebhookRepository;
use Illuminate\Console\Command;
use Throwable;

class UpdateOrderFulfillViaWebhook extends Command
{
    protected $signature = 'order-fulfill:update-via-webhook';

    protected $description = 'Update order fulfill status via webhook callback';

    public function handle(): int
    {
        try {
            $controller = new FulfillController();
            $arrFulfillStatus = OrderProductFulfillStatus::getForUpdate(false);
            $data = ApiLog::query()
                ->select([
                    'request_body',
                    'reference_id',
                ])
                ->with([
                    'fulfill_order.order_products'
                ])
                ->where('type', ApiLogTypeEnum::WEBHOOK)
                ->where('reference_type', AccessExternalApiType::SUPPLIER)
                ->where('created_at', '>=', now()->subHours(3))
                ->whereHas('fulfill_order.order_products', function($q) use ($arrFulfillStatus)  {
                    $q->whereIn('fulfill_status', $arrFulfillStatus);
                })
                ->whereIn('reference_id', [
                    SupplierEnum::QTCO,
                    SupplierEnum::PRINTGEEK,
                ])
                ->get();
            foreach($data as $each){
                $supplierId = $each->reference_id;
                $arr = json_decode($each->request_body, true);

                switch ($supplierId){
                    case SupplierEnum::QTCO:
                        $response = (new QTCOWebhookRepository($arr, $supplierId))
                            ->get();
                        break;
                    case SupplierEnum::PRINTGEEK:
                        $response = (new PrintGeekWebhookRepository($arr, $supplierId))
                            ->get();
                        break;
                }

                if (isset($response)) {
                    $controller::updateByResponse($response);
                }
            }

            return self::SUCCESS;
        } catch (Throwable $e) {
            logException($e);
        }

        return self::FAILURE;
    }
}
