<?php

namespace App\Library\PingPongX;

use App\Enums\CacheKeys;
use App\Enums\DiscordUserIdEnum;
use App\Enums\EnvironmentEnum;
use App\Enums\PingPongX\PingPongXHookEventNameEnum;
use App\Enums\PingPongX\PingPongXOrderTypeEnum;
use App\Models\SystemConfig;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

/**
 * @ref https://test2-business-cgi.pingpongx.com/static/partner/v3/_book/Sign/Sign.html
 */
class PingPongX
{
    protected string $baseUrl = 'https://test2-business.pingpongx.com';
    protected string $baseApiUrl = 'https://test2-business-cgi.pingpongx.com';
    protected array $config = [
        'appId' => null,
        'appSecret' => null
    ];

    protected array $paths = [
        'oauthToken' => '/v3/b2b/oauth/token',
        'queryFeeAndFx' => '/v3/b2b/order/fx',
        'createRecipient' => '/v3/b2b/recipient/create',
        'createOrder' => '/v3/b2b/order/payout',
        'queryOrderStatus' => '/v3/b2b/order/status',
        'subscribeHookUrl' => '/v3/b2b/notification/config',
        'queryAccountBalance' => '/v3/b2b/account/balance/all',
        'queryRecipients' => '/v3/b2b/recipient/list',
        'queryRecipient' => '/v3/b2b/recipient/get',
        'deleteRecipient' => '/v3/b2b/recipient/delete'
    ];

    protected static $instance = null;

    public function __construct()
    {
        if (app()->environment(EnvironmentEnum::PRODUCTION)) {
            $this->baseUrl = 'https://business.pingpongx.com';
            $this->baseApiUrl = 'https://business-cgi.pingpongx.com';
        }

        $config = config('senprints.pingpongx');
        $this->config['appId'] = $config['app_id'] ?? null;
        $this->config['appSecret'] = $config['app_secret'] ?? null;
    }

    public static function instance()
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * @param string $redirectUri
     * @param string $state
     * @return string
     */
    public function getAuthorizationUrl(string $redirectUri = '', string $state = ''): string
    {
        $queryParam = [
            'responseType' => 'code',
            'appId' => $this->config['appId'],
            'redirectUri' => $redirectUri,
            'state' => $state
        ];
        return $this->baseUrl . '/entrance/authorization?' . http_build_query($queryParam);
    }

    /**
     * @param $refreshToken
     * @return mixed|null
     */
    public function refreshToken($refreshToken)
    {
        $params = [
            'app_id' => $this->config['appId'],
            'app_secret' => $this->config['appSecret'],
            'grant_type' => 'refresh_token',
            'refresh_token' => $refreshToken,
        ];
        try {
            $response = Http::withoutVerifying()
                ->post($this->getBaseApiUrl('oauthToken'), $params)
                ->json();
            if ($response) {
                if ($response['success']) {
                    $data = $response['data'] ?? null;
                    if (is_null($data)) {
                        graylogError("PingPongX refreshToken Error: Can not parse data in response \r appId: {$this->config['appId']} \r appSecret: {$this->config['appSecret']} \r refreshToken: {$refreshToken}", [
                            'category' => 'process_payout_pingpongx_errors',
                            'user_type' => 'system',
                            'user_id' => null,
                            'action' => 'request'
                        ]);
                        return null;
                    }
                    return $data;
                }

                $message = $response['message'] ?? null;
                if (!is_null($message)) {
                    graylogError("PingPongX refreshToken Error: " . $message, [
                        'category' => 'process_payout_pingpongx_errors',
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => 'request'
                    ]);
                }
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX refreshToken Error: {$e->getMessage()}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param string $code
     * @return mixed|null
     */
    public function authorization(string $code = '')
    {
        if (empty($code)) {
            return null;
        }
        $params = [
            'app_id' => $this->config['appId'],
            'app_secret' => $this->config['appSecret'],
            'grant_type' => 'authorization_code',
            'code' => $code,
        ];
        try {
            $response = Http::withoutVerifying()
                ->post($this->getBaseApiUrl('oauthToken'), $params)
                ->json();
            if ($response) {
                if ($response['success']) {
                    $data = $response['data'] ?? null;
                    if (is_null($data)) {
                        graylogError("PingPongX Authorization Error: Can not parse data in response \r appId: {$this->config['appId']} \r appSecret: {$this->config['appSecret']} \r authCode: {$code}", [
                            'category' => 'process_payout_pingpongx_errors',
                            'user_type' => 'system',
                            'user_id' => null,
                            'action' => 'request'
                        ]);
                        return null;
                    }
                    return $data;
                }

                $message = $response['message'] ?? null;
                if (!is_null($message)) {
                    graylogError("PingPongX Authorization Error: " . $message, [
                        'category' => 'process_payout_pingpongx_errors',
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => 'request'
                    ]);
                }
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX Authorization Error: {$e->getMessage()}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param array $requestParams
     * @param $timestamp
     * @return string|null
     */
    public function generateSignature(array $requestParams = [], $timestamp)
    {
        if (empty($requestParams)) {
            return null;
        }
        $requestParams["timestamp"] = $timestamp;
        $requestParams["appId"] = $this->config['appId'];
        ksort($requestParams);
        $requestParams["sp___none"] = $this->config['appSecret'];

        $requestParamsString = implode('', array_map(
            function ($requestValue, $requestKey) {
                $requestKey = str_replace("sp___none", "", $requestKey);
                if (is_array($requestValue)) {
                    $requestValue = json_encode($requestValue);
                }
                return sprintf("%s%s", $requestKey, $requestValue);
            },
            $requestParams,
            array_keys($requestParams)
        ));
        return hash('sha256', $requestParamsString);
    }

    /**
     * @param string $email
     * @return mixed|null
     */
    public function createRecipient(string $email)
    {
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        $params = [
            'open_id' => $pingpongConfig['open_id'],
            'holder_type' => 'Personal',
            'account_type' => 'RECIPIENT_PP',
            'recipient_detail' => [
                'recipient_type' => 'Other',
                'recipient_location' => 'VietNam',
                'pp_account' => $email
            ]
        ];
        $requestTimestamp = now()->timestamp;
        $sign = $this->generateSignature($params, $requestTimestamp);
        try {
            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->post($this->getBaseApiUrl('createRecipient'), $params)
                ->json();
            if ($response) {
                if ($response['success']) {
                    $data = $response['data'] ?? null;
                    if (is_null($data)) {
                        graylogError("PingPongX createRecipient Error: Can not parse data in response \r ppAccount: {$email}", [
                            'category' => 'process_payout_pingpongx_errors',
                            'user_type' => 'system',
                            'user_id' => null,
                            'action' => 'request'
                        ]);
                        return null;
                    }
                    return $data;
                }

                $message = $response['message'] ?? null;
                if ($response['code'] === 40006 && $message === 'this recipient has exist') {
                    $queryRecipients = $this->queryRecipients();
                    if (!empty($queryRecipients)) {
                        $foundAccountDetail = null;
                        foreach ($queryRecipients as $recipient) {
                            $recipientDetail = $recipient['recipient_detail'] ?? null;
                            if (!is_null($recipientDetail) && $recipientDetail['pp_account'] === $email) {
                                $queryRecipient = $this->queryRecipient($recipient['biz_id']);
                                if (!is_null($queryRecipient)) {
                                    $foundAccountDetail = $recipient;
                                    break;
                                }
                            }
                        }
                        unset($queryRecipients);
                        return $foundAccountDetail ?? null;
                    }
                } else {
                    $this->notificationNeedToReAuthorizationAccount($message);
                    graylogError("PingPongX createRecipient Error: " . $message . " - Params: " . json_encode($params), [
                        'category' => 'process_payout_pingpongx_errors',
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => 'request'
                    ]);
                    return [
                        'status' => false,
                        'createRecipientError' => true,
                        'message' => $message
                    ];
                }
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX createRecipient Error: {$e->getMessage()} \r ppAccount: {$email}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param $amount
     * @param $pingpongConfig
     * @return mixed|null
     */
    public function queryOrderFx($amount, $pingpongConfig = null)
    {
        $pingpongConfig = $pingpongConfig ?? SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        try {
            $params = [
                'open_id' => $pingpongConfig['open_id'],
                'origin_currency' => 'USD',
                'target_currency' => 'USD',
                'order_type' => PingPongXOrderTypeEnum::EXCHANGE,
                'amount' => abs($amount)
            ];
            $requestTimestamp = now()->timestamp;
            $sign = $this->generateSignature($params, $requestTimestamp);

            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->post($this->getBaseApiUrl('queryFeeAndFx'), $params)
                ->json();
            if ($response) {
                if ($response['success']) {
                    $data = $response['data'] ?? null;
                    if (is_null($data)) {
                        graylogError("PingPongX queryOrderFx Error: Can not parse data in response \r amount: {$amount}", [
                            'category' => 'process_payout_pingpongx_errors',
                            'user_type' => 'system',
                            'user_id' => null,
                            'action' => 'request'
                        ]);
                        return null;
                    }
                    return $data['lock_rate_id'] ?? null;
                }

                $message = $response['message'] ?? null;
                if (!is_null($message)) {
                    $this->notificationNeedToReAuthorizationAccount($message);
                    graylogError("PingPongX queryOrderFx Error: {$message} \r amount: {$amount}", [
                        'category' => 'process_payout_pingpongx_errors',
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => 'request'
                    ]);
                }
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX queryOrderFx Error: {$e->getMessage()} \r amount: {$amount}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param array $transactions
     * @return array|null
     */
    public function createOrder(array $transactions = []): ?array
    {
        if (empty($transactions)) {
            return null;
        }
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            $transactionsDebug = json_encode($transactions);
            graylogError("PingPongX createOrder Error: getPingPongXToken \r transaction: {$transactionsDebug}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
        try {
            return Http::pool(function (Pool $pool) use ($pingpongConfig, $transactions) {
                collect($transactions)->each(function ($order) use ($pingpongConfig, $pool) {
                    $lockRateId = $this->queryOrderFx($order['amount'], $pingpongConfig);
                    if (!is_null($lockRateId)) {
                        $order['open_id'] = $pingpongConfig['open_id'];
                        $order['rate_id'] = $lockRateId;
                        $order['partner_order_id'] = $order['bill_id'];
                        unset($order['bill_id']);
                        $requestTimestamp = now()->timestamp;
                        $sign = $this->generateSignature($order, $requestTimestamp);
                        $pool->withToken($pingpongConfig['access_token'])->withHeaders([
                            'timestamp' => $requestTimestamp,
                            'sign' => $sign,
                            'app_id' => $this->config['appId']
                        ])
                        ->post($this->getBaseApiUrl('createOrder'), $order);
                    } else {
                        graylogError("PingPongX createOrder Error: Can not get lockRateId \r transaction: " . json_encode($order), [
                            'category' => 'process_payout_pingpongx_errors',
                            'user_type' => 'system',
                            'user_id' => null,
                            'action' => 'request'
                        ]);
                    }
                });
            });
        } catch (\Throwable $e) {
            $transactionsDebug = json_encode($transactions);
            graylogError("PingPongX createOrder: {$e->getMessage()} \r transaction: {$transactionsDebug}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param $hookUrl
     * @param $hookEventName
     * @return mixed|null
     */
    public function subscribeHookUrl($hookUrl, $hookEventName)
    {
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        if (!in_array($hookEventName, PingPongXHookEventNameEnum::getValues())) {
            return null;
        }
        $params = [
            'event_type' => $hookEventName,
            'hook' => $hookUrl,
            'app_id' => $this->config['appId']
        ];
        $requestTimestamp = now()->timestamp;
        $sign = $this->generateSignature($params, $requestTimestamp);
        try {
            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->post($this->getBaseApiUrl('subscribeHookUrl'), $params)
                ->json();
            if ($response) {
                if ($response['success']) {
                    $data = $response['data'] ?? null;
                    if (is_null($data)) {
                        graylogError("PingPongX subscribeHookUrl Error: Can not parse data in response \r hookUrl: {$hookUrl} \r hookEventName: {$hookEventName}", [
                            'category' => 'process_payout_pingpongx_errors',
                            'user_type' => 'system',
                            'user_id' => null,
                            'action' => 'request'
                        ]);
                        return null;
                    }
                    return $data;
                }

                $message = $response['message'] ?? null;
                if (!is_null($message)) {
                    graylogError("PingPongX subscribeHookUrl Error: {$message} \r hookUrl: {$hookUrl} \r hookEventName: {$hookEventName}", [
                        'category' => 'process_payout_pingpongx_errors',
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => 'request'
                    ]);
                }
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX subscribeHookUrl Error: {$e->getMessage()} \r hookUrl: {$hookUrl} \r hookEventName: {$hookEventName}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param $orderId
     * @return mixed|null
     */
    public function queryOrderStatus($orderId)
    {
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        $params = [
            'open_id' => $pingpongConfig['open_id'],
            'order_id' => $orderId
        ];
        $requestTimestamp = now()->timestamp;
        $sign = $this->generateSignature($params, $requestTimestamp);
        try {
            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->post($this->getBaseApiUrl('queryOrderStatus'), $params)
                ->json();
            if ($response) {
                if ($response['success'] && !is_null($response['data'])) {
                    return $response['data'];
                }
                graylogError("PingPongX queryOrderStatus Error: Can not parse data in response \r orderId: {$orderId} \r response: " . json_encode($response), [
                    'category' => 'process_payout_pingpongx_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'request'
                ]);
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX queryOrderStatus Error: {$e->getMessage()} \r orderId: {$orderId}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param $partnerOrderId
     * @return mixed|null
     */
    public function queryOrderStatusByPartnerOrderId($partnerOrderId)
    {
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        $params = [
            'open_id' => $pingpongConfig['open_id'],
            'partner_order_id' => $partnerOrderId
        ];
        $requestTimestamp = now()->timestamp;
        $sign = $this->generateSignature($params, $requestTimestamp);
        try {
            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->post($this->getBaseApiUrl('queryOrderStatus'), $params)
                ->json();
            if ($response) {
                if ($response['success'] && !is_null($response['data'])) {
                    return $response['data'];
                }
                graylogError("PingPongX queryOrderStatus Error: Can not parse data in response \r partnerOrderId: {$partnerOrderId} \r response: " . json_encode($response), [
                    'category' => 'process_payout_pingpongx_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'request'
                ]);
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX queryOrderStatus Error: {$e->getMessage()} \r partnerOrderId: {$partnerOrderId}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @return mixed|void|null
     */
    public function queryAccountBalance()
    {
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        $params = [
            'open_id' => $pingpongConfig['open_id']
        ];
        $requestTimestamp = now()->timestamp;
        $sign = $this->generateSignature($params, $requestTimestamp);
        try {
            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->get($this->getBaseApiUrl('queryAccountBalance'), $params)
                ->json();
            if ($response) {
                if ($response['success'] && !is_null($response['data'])) {
                    return $response['data'];
                }
                graylogError("PingPongX queryAccountBalance Error: Can not parse data in response \r response: " . json_encode($response), [
                    'category' => 'process_payout_pingpongx_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'request'
                ]);
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX queryAccountBalance Error: {$e->getMessage()}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
        }
    }

    /**
     * @param string $encrypted
     * @param $notificationType
     * @return false|string|null
     */
    public function decryptNotification(string $encrypted, $notificationType)
    {
        if (!in_array($notificationType, PingPongXHookEventNameEnum::getValues())) {
            return null;
        }
        $config = SystemConfig::getPingPongXSubscribeNotificationSecretKey();
        if (is_null($config)) {
            return null;
        }
        $pingpongSubscribeNotificationConfig = $config[$notificationType] ?? null;
        if (is_null($pingpongSubscribeNotificationConfig)) {
            return null;
        }
        $encrypted = \base64_decode($encrypted);
        $decryptString = \openssl_decrypt($encrypted, 'AES-256-ECB', $pingpongSubscribeNotificationConfig['encryptKey'], OPENSSL_RAW_DATA);
        return $decryptString ?? null;
    }

    /**
     * @param $pathName
     * @return string|null
     */
    protected function getBaseApiUrl($pathName): ?string
    {
        $pathUri = $this->paths[$pathName] ?? null;
        if (is_null($pathUri)) {
            return null;
        }
        return $this->baseApiUrl . $pathUri;
    }

    /**
     * @return bool
     */
    public static function whiteListEnabled(): bool
    {
        $config = SystemConfig::checkPingPongXWhiteListEnabled();
        if (is_null($config)) {
            return false;
        }
        return (bool)$config;
    }

    /**
     * @return bool
     */
    public static function quickTestEnabled(): bool
    {
        $config = SystemConfig::checkPingPongXPayoutQuickTestEnabled();
        if (is_null($config)) {
            return false;
        }
        return (bool)$config;
    }

    /**
     * @param int $pageNumber
     * @param string $status
     * @param int $pageSize
     * @return mixed|null
     */
    public function queryRecipients(int $pageNumber = 1, string $status = 'AVAILABLE', int $pageSize = 200)
    {
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        $params = [
            'open_id' => $pingpongConfig['open_id'],
            'account_type' => 'RECIPIENT_PP',
            'status' => $status,
            'page_no' => $pageNumber,
            'page_size' => $pageSize
        ];
        $requestTimestamp = now()->timestamp;
        $sign = $this->generateSignature($params, $requestTimestamp);
        try {
            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->post($this->getBaseApiUrl('queryRecipients'), $params)
                ->json();
            if ($response) {
                if ($response['success'] && !is_null($response['data']) && !is_null($response['data']['records'])) {
                    return $response['data']['records'];
                }
                graylogError("PingPongX queryRecipients Error: Can not parse data in response \r response: " . json_encode($response), [
                    'category' => 'process_payout_pingpongx_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'request'
                ]);
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX queryRecipients Error: {$e->getMessage()}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param string $bizId
     * @return mixed|null
     */
    public function queryRecipient(string $bizId)
    {
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        $params = [
            'open_id' => $pingpongConfig['open_id'],
            'biz_id' => $bizId
        ];
        $requestTimestamp = now()->timestamp;
        $sign = $this->generateSignature($params, $requestTimestamp);
        try {
            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->post($this->getBaseApiUrl('queryRecipient'), $params)
                ->json();
            if ($response) {
                if ($response['success'] && !is_null($response['data']) && !is_null($response['data']['biz_id'])) {
                    return $response['data'];
                }
                graylogError("PingPongX queryRecipient Error: Can not parse data in response \r response: " . json_encode($response), [
                    'category' => 'process_payout_pingpongx_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'request'
                ]);
                return null;
            }
            return null;
        } catch (\Exception $e) {
            graylogError("PingPongX queryRecipient Error: {$e->getMessage()}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return null;
        }
    }

    /**
     * @param string $bizId
     * @return bool|null
     */
    public function deleteRecipient(string $bizId): ?bool
    {
        $pingpongConfig = SystemConfig::getPingPongXToken();
        if (is_null($pingpongConfig)) {
            return null;
        }
        $params = [
            'open_id' => $pingpongConfig['open_id'],
            'biz_id' => $bizId
        ];
        $requestTimestamp = now()->timestamp;
        $sign = $this->generateSignature($params, $requestTimestamp);
        try {
            $response = Http::withoutVerifying()
                ->withToken($pingpongConfig['access_token'])
                ->withHeaders([
                    'timestamp' => $requestTimestamp,
                    'sign' => $sign,
                    'app_id' => $this->config['appId']
                ])
                ->post($this->getBaseApiUrl('deleteRecipient'), $params)
                ->json();
            if ($response) {
                if ($response['success'] && !is_null($response['message']) && $response['message'] === 'OK') {
                    return true;
                }
                graylogError("PingPongX deleteRecipient Error: Can not parse data in response \r response: " . json_encode($response), [
                    'category' => 'process_payout_pingpongx_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'request'
                ]);
                return false;
            }
            return false;
        } catch (\Exception $e) {
            graylogError("PingPongX deleteRecipient Error: {$e->getMessage()}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'request'
            ]);
            return false;
        }
    }

    /**
     * @param $message
     * @return void
     */
    private function notificationNeedToReAuthorizationAccount($message): void
    {
        if (!str_contains($message, 'access token passed is incorrect or expired') || !app()->isProduction()) {
            return;
        }
        cache()->forget(CacheKeys::PINGPONGX_AUTHORIZATION);
        $cacheKey = 'pingpongx_notification_need_to_re_authorization_account';
        if (Cache::get($cacheKey)) {
            return;
        }
        Cache::put($cacheKey, md5($cacheKey), now()->addHours(12));
        $message .= '. Please click re-authorize the account at https://admin.senprints.com/payout';
        logToDiscord($message . PHP_EOL . 'cc: a ' . mentionDiscord(DiscordUserIdEnum::PHUC_TRINH), 'payout');
    }
}
